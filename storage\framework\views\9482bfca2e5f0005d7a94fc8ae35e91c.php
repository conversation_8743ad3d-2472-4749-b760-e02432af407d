<?php
    Theme::set('stickyHeader', 'false');
    Theme::set('topHeader', Theme::partial('header-product-page', compact('product')));
    Theme::set('bottomFooter', Theme::partial('footer-product-page', compact('product')));
    Theme::set('pageId', 'product-page');
    Theme::set('headerMobile', Theme::partial('header-mobile-product'));
?>

<div class="ps-page--product">
    <div class="container" id="app">
            <div class="ps-page__container">
                <div class="ps-page__left">
                    <div class="ps-product--detail ps-product--fullwidth">
                        <div class="ps-product__header">
                            <div class="ps-product__thumbnail" data-vertical="true">
                                <figure>
                                    <div class="ps-wrapper">
                                        <div class="ps-product__gallery" data-arrow="false">
                                            <?php $__currentLoopData = $productImages; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $img): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                <div class="item">
                                                    <a href="<?php echo e(RvMedia::getImageUrl($img)); ?>">
                                                        <img src="<?php echo e(RvMedia::getImageUrl($img)); ?>" alt="<?php echo e($product->name); ?>"/>
                                                    </a>
                                                </div>
                                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                        </div>
                                    </div>
                                </figure>
                                <div class="ps-product__variants" data-item="4" data-md="4" data-sm="4" data-arrow="true">
                                    <?php $__currentLoopData = $productImages; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $img): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <div class="item">
                                            <img src="<?php echo e(RvMedia::getImageUrl($img, 'thumb')); ?>" alt="<?php echo e($product->name); ?>"/>
                                        </div>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                </div>
                            </div>
                            <div class="ps-product__info">
                                <h1><?php echo e($product->name); ?></h1>


                                 <!-- Model Name Accordion -->
                                <div class="accordion-item-custom">
                                    <div class="info-row accordion-header" data-bs-toggle="collapse" data-bs-target="#modelNameCollapse" aria-expanded="false" style="cursor: pointer;">
                                        <div class="info-label">Model Name</div>
                                        <div class="info-value">
                                            <?php if($product->models->count()): ?>
                                                <?php echo e($product->models->first()->name); ?>

                                                <i class="fas fa-external-link-alt ms-1" style="font-size: 0.75rem;"></i>
                                            <?php endif; ?>
                                        </div>
                                        <i class="fas fa-chevron-down dropdown-icon ms-auto accordion-chevron"></i>
                                    </div>
                                    <div class="collapse" id="modelNameCollapse">
                                        <div class="accordion-content">
                                            <?php if($product->models->count()): ?>
                                                <?php $__currentLoopData = $product->models->skip(1); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $model): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                    <div class="accordion-list-item">
                                                        <i class="fas fa-check-circle text-primary me-2"></i>
                                                        <a href="<?php echo e($model->url); ?>"><?php echo e($model->name); ?></a>
                                                    </div>
                                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                            <?php endif; ?>
                                        </div>
                                    </div>
                                </div>

                                <!-- Series Accordion -->
                                <div class="accordion-item-custom">
                                    <div class="info-row accordion-header" data-bs-toggle="collapse" data-bs-target="#seriesCollapse" aria-expanded="false" style="cursor: pointer;">
                                        <div class="info-label">Series</div>
                                        <div class="info-value">
                                            <?php if($product->categories->count()): ?>
                                                <?php echo e($product->categories->first()->name); ?>

                                                <i class="fas fa-external-link-alt ms-1" style="font-size: 0.75rem;"></i>
                                            <?php endif; ?>
                                        </div>
                                        <i class="fas fa-chevron-down dropdown-icon ms-auto accordion-chevron"></i>
                                    </div>
                                    <div class="collapse" id="seriesCollapse">
                                        <div class="accordion-content">
                                            <?php if($product->categories->count()): ?>
                                                <?php $__currentLoopData = $product->categories->skip(1); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $category): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                    <div class="accordion-list-item">
                                                        <i class="fas fa-check-circle text-primary me-2"></i>
                                                        <a href="<?php echo e($category->url); ?>"><?php echo e($category->name); ?></a>
                                                    </div>
                                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                            <?php endif; ?>
                                        </div>
                                    </div>
                                </div>


            <!-- Data Section Accordion -->
            <div class="data-section">
                <div class="data-header accordion-header" data-bs-toggle="collapse" data-bs-target="#dataCollapse" aria-expanded="false" style="cursor: pointer;">
                    <span>Data</span>
                    <span>Catalog-Numalliance-CNC Wire Bender Robomac e-Motion</span>
                    <i class="fas fa-chevron-down dropdown-icon accordion-chevron"></i>
                </div>
                <div class="collapse" id="dataCollapse">
                    <div class="accordion-content">
                        <div class="accordion-list-item">
                            
                            Catalog-Numalliance-CNC Wire Bender Robomac e-Motion (Current)
                        </div>
                        <div class="accordion-list-item">
                            
                            Technical Specifications Sheet
                        </div>
                        <div class="accordion-list-item">
                            
                            Product Images Gallery
                        </div>
                        <div class="accordion-list-item">
                            
                            Operation Manual Video
                        </div>
                    </div>
                </div>
            </div>

            <?php if(is_plugin_active('marketplace') && $product->store_id): ?>
            <!-- Manufacturer Section -->
            <div class="manufacturer-section">
                <h2 class="manufacturer-title">Manufacturer information</h2>

                <a href="<?php echo e($product->store->url); ?>" class="manufacturer-info">
                    <div class="manufacturer-logo">
                        <img src="<?php echo e(RvMedia::getImageUrl($product->store->logo, 'full', false, RvMedia::getDefaultImage())); ?>" alt="<?php echo e($product->store->name); ?>">
                    </div>
                    <div class="manufacturer-details">
                        <h5>
                            <?php echo e($product->store->name); ?>

                            <i class="fas fa-check-circle verified-badge"></i>
                            <i class="fas fa-chevron-right ms-1" style="color: #5f6368; font-size: 0.75rem;"></i>
                        </h5>
                        <p class="manufacturer-description">
                            <?php echo BaseHelper::clean($product->store->description); ?>

                        </p>
                    </div>
                </a>

                <!-- Action Buttons -->
                <div class="action-buttons">
                    <button class="btn btn-inquiry">
                        <i class="fas fa-comment-dots me-2"></i>Inquiry
                    </button>
                    <button class="btn btn-order">
                        How to order
                    </button>
                </div>

                <!-- Problem Section -->
                <div class="problem-section">
                    Problem with product info?
                    <a href="#" class="problem-link">
                        Update request <i class="fas fa-chevron-right ms-1"></i>
                    </a>
                </div>

            </div>
             <?php endif; ?>
                                

                                
                                
                        </div>
                        </div>
                        <div class="ps-product__content ps-tab-root">
                            <ul class="ps-tab-list">
                                <li class="active"><a href="#tab-description"><?php echo e(__('Description')); ?></a></li>
                                <?php if(EcommerceHelper::isProductSpecificationEnabled() && $product->specificationAttributes->where('pivot.hidden', false)->isNotEmpty()): ?>
                                    <li><a href="#tab-specification"><?php echo e(__('Specification')); ?></a></li>
                                <?php endif; ?>
                                <?php if(EcommerceHelper::isReviewEnabled()): ?>
                                    <li><a href="#tab-reviews"><?php echo e(__('Reviews')); ?>&nbsp;(<?php echo e($product->reviews_count); ?>)</a></li>
                                <?php endif; ?>
                                <?php if(is_plugin_active('marketplace') && $product->store_id): ?>
                                    <li><a href="#tab-vendor"><?php echo e(__('Vendor')); ?></a></li>
                                <?php endif; ?>
                                <?php if(is_plugin_active('faq')): ?>
                                    <?php if(count($product->faq_items) > 0): ?>
                                        <li><a href="#tab-faq"><?php echo e(__('Questions and Answers')); ?></a></li>
                                    <?php endif; ?>
                                <?php endif; ?>
                            </ul>
                            <div class="ps-tabs">
                                <div class="ps-tab active" id="tab-description">
                                    <div class="ps-document">
                                         <div class="product-description-table2">
                                            <table class="table table-striped">
                                                <tr>
                                                    <th><?php echo e(__('Product Name')); ?></th>
                                                    <td><?php echo e($product->name); ?></td>
                                                </tr>
                                                <?php if($product->models->count()): ?>
                                                <tr>
                                                    <th><?php echo e(__('Model Number')); ?></th>
                                                    <td>
                                                        <?php $__currentLoopData = $product->models; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $model): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                            <a href="<?php echo e($model->url); ?>"><?php echo e($model->name); ?></a><?php if(!$loop->last): ?>,<?php endif; ?>
                                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                                    </td>
                                                </tr>
                                                <?php endif; ?>
                                                <?php if($product->brand_id): ?>
                                                <tr>
                                                    <th><?php echo e(__('Brand')); ?></th>
                                                    <td><?php echo e($product->brand->name); ?></td>
                                                </tr>
                                                <?php endif; ?>
                                                <?php if($product->categories->count()): ?>
                                                <tr>
                                                    <th><?php echo e(__('Category')); ?></th>
                                                    <td>
                                                        <?php $__currentLoopData = $product->categories; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $category): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                            <a href="<?php echo e($category->url); ?>"><?php echo BaseHelper::clean($category->name); ?></a><?php if(!$loop->last): ?>,<?php endif; ?>
                                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                                    </td>
                                                </tr>
                                                <?php endif; ?>
                                                <?php if($product->country_of_origin): ?>
                                                <tr>
                                                    <th><?php echo e(__('Country of Origin')); ?></th>
                                                    <td><?php echo e($product->country_of_origin); ?></td>
                                                </tr>
                                                <?php endif; ?>
                                                <?php if($product->material): ?>
                                                <tr>
                                                    <th><?php echo e(__('Material')); ?></th>
                                                    <td><?php echo e($product->material); ?></td>
                                                </tr>
                                                <?php endif; ?>
                                                <?php if($product->applications): ?>
                                                <tr>
                                                    <th><?php echo e(__('Applications')); ?></th>
                                                    <td><?php echo e($product->applications); ?></td>
                                                </tr>
                                                <?php endif; ?>



                                            </table>

                                        </div>
                                        <div class="ck-content">
                                            <?php echo BaseHelper::clean($product->content); ?>

                                        </div>
                                        <br />
                                        <?php echo apply_filters(BASE_FILTER_PUBLIC_COMMENT_AREA, null, $product); ?>

                                    </div>
                                </div>

                                <?php if(EcommerceHelper::isProductSpecificationEnabled() && $product->specificationAttributes->where('pivot.hidden', false)->isNotEmpty()): ?>
                                    <div class="ps-tab" id="tab-specification">
                                        <div class="tp-product-details-additional-info">
                                            <?php echo $__env->make(EcommerceHelper::viewPath('includes.product-specification'), array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
                                        </div>
                                    </div>
                                <?php endif; ?>

                                <?php if(EcommerceHelper::isReviewEnabled()): ?>
                                    <div class="ps-tab" id="tab-reviews">
                                        <?php echo $__env->make('plugins/ecommerce::themes.includes.reviews', ['reviewButtonClass' => 'ps-btn'], array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
                                    </div>
                                <?php endif; ?>

                                <?php if(is_plugin_active('marketplace') && $product->store_id): ?>
                                    <div class="ps-tab" id="tab-vendor">
                                        <h4><?php echo e($product->store->name); ?></h4>
                                        <div>
                                            <?php echo BaseHelper::clean($product->store->content); ?>

                                        </div>

                                        <a href="<?php echo e($product->store->url); ?>" class="more-products">
                                            <?php echo e(__('More Products from :store',  ['store' => $product->store->name])); ?>

                                        </a>
                                    </div>
                                <?php endif; ?>

                                <?php if(is_plugin_active('faq') && count($product->faq_items) > 0): ?>
                                    <div class="ps-tab" id="tab-faq">
                                        <div class="accordion" id="faq-accordion">
                                            <?php $__currentLoopData = $product->faq_items; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $faq): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                <div class="card">
                                                    <div class="card-header" id="heading-faq-<?php echo e($loop->index); ?>">
                                                        <h2 class="mb-0">
                                                            <button class="btn btn-link btn-block text-left <?php if(!$loop->first): ?> collapsed <?php endif; ?>" type="button" data-toggle="collapse" data-target="#collapse-faq-<?php echo e($loop->index); ?>" aria-expanded="true" aria-controls="collapse-faq-<?php echo e($loop->index); ?>">
                                                                <?php echo BaseHelper::clean($faq[0]['value']); ?>

                                                            </button>
                                                        </h2>
                                                    </div>

                                                    <div id="collapse-faq-<?php echo e($loop->index); ?>" class="collapse <?php if($loop->first): ?> show <?php endif; ?>" aria-labelledby="heading-faq-<?php echo e($loop->index); ?>" data-parent="#faq-accordion">
                                                        <div class="card-body">
                                                            <?php echo BaseHelper::clean($faq[1]['value']); ?>

                                                        </div>
                                                    </div>
                                                </div>
                                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                        </div>
                                    </div>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                </div>
                
            </div>

            </div>

            

            <div class="ps-section--default">
                <div class="ps-section__header">
                    <h3><?php echo e(__('Related products')); ?></h3>
                </div>
                <div class="ps-section__content">
                    <div class="ps-carousel--responsive owl-slider"
                         data-owl-auto="true"
                         data-owl-loop="false"
                         data-owl-speed="10000"
                         data-owl-gap="10"
                         data-owl-nav="false"
                         data-owl-dots="true"
                         data-owl-item="5"
                         data-owl-item-xs="2"
                         data-owl-item-sm="3"
                         data-owl-item-md="3"
                         data-owl-item-lg="4"
                         data-owl-item-xl="5"
                         data-owl-duration="1000"
                         data-owl-mousedrag="on"
                    >
                        <?php $__currentLoopData = $product->store->products()->take(7)->get(); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $product): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <div class="ps-product">
                                <?php echo Theme::partial('product-item', ['product' => $product]); ?>

                            </div>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </div>
                </div>
            </div>
    </div>
</div>
<?php /**PATH D:\laragon\www\muhrak\platform\themes/muhrak/views/ecommerce/product.blade.php ENDPATH**/ ?>