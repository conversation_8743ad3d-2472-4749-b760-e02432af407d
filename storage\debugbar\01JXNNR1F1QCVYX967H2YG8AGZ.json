{"__meta": {"id": "01JXNNR1F1QCVYX967H2YG8AGZ", "datetime": "2025-06-13 22:14:42", "utime": **********.402585, "method": "GET", "uri": "/admin/media/list?view_type=tiles&filter=everything&view_in=all_media&sort_by=created_at-desc&folder_id=0&search=&multiple=false&type=*&open_in=modal&load_more_file=false&paged=1&posts_per_page=40", "ip": "127.0.0.1"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.186774, "end": **********.402604, "duration": 1.2158300876617432, "duration_str": "1.22s", "measures": [{"label": "Booting", "start": **********.186774, "relative_start": 0, "end": **********.867923, "relative_end": **********.867923, "duration": 0.****************, "duration_str": "681ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.867934, "relative_start": 0.****************, "end": **********.402607, "relative_end": 2.86102294921875e-06, "duration": 0.****************, "duration_str": "535ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.889979, "relative_start": 0.***************, "end": **********.897433, "relative_end": **********.897433, "duration": 0.0074541568756103516, "duration_str": "7.45ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "View: __components::9a1da6c7f662474948fe63691d3a1543", "start": **********.966846, "relative_start": 0.****************, "end": **********.966846, "relative_end": **********.966846, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::a460cf1eef36b568db70ad3c1eead84e", "start": **********.290523, "relative_start": 1.****************, "end": **********.290523, "relative_end": **********.290523, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::6efe2c37a540485ed4ad0a3be41e6665", "start": **********.301826, "relative_start": 1.1150519847869873, "end": **********.301826, "relative_end": **********.301826, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::6efe2c37a540485ed4ad0a3be41e6665", "start": **********.304098, "relative_start": 1.117323875427246, "end": **********.304098, "relative_end": **********.304098, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::6efe2c37a540485ed4ad0a3be41e6665", "start": **********.306156, "relative_start": 1.1193819046020508, "end": **********.306156, "relative_end": **********.306156, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::6efe2c37a540485ed4ad0a3be41e6665", "start": **********.308546, "relative_start": 1.121772050857544, "end": **********.308546, "relative_end": **********.308546, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::6efe2c37a540485ed4ad0a3be41e6665", "start": **********.311264, "relative_start": 1.1244900226593018, "end": **********.311264, "relative_end": **********.311264, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::6efe2c37a540485ed4ad0a3be41e6665", "start": **********.313838, "relative_start": 1.1270639896392822, "end": **********.313838, "relative_end": **********.313838, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::6efe2c37a540485ed4ad0a3be41e6665", "start": **********.316251, "relative_start": 1.1294770240783691, "end": **********.316251, "relative_end": **********.316251, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::6efe2c37a540485ed4ad0a3be41e6665", "start": **********.318362, "relative_start": 1.1315879821777344, "end": **********.318362, "relative_end": **********.318362, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::a460cf1eef36b568db70ad3c1eead84e", "start": **********.320556, "relative_start": 1.133781909942627, "end": **********.320556, "relative_end": **********.320556, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::6efe2c37a540485ed4ad0a3be41e6665", "start": **********.323133, "relative_start": 1.1363589763641357, "end": **********.323133, "relative_end": **********.323133, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::6efe2c37a540485ed4ad0a3be41e6665", "start": **********.325744, "relative_start": 1.138969898223877, "end": **********.325744, "relative_end": **********.325744, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::6efe2c37a540485ed4ad0a3be41e6665", "start": **********.327972, "relative_start": 1.141197919845581, "end": **********.327972, "relative_end": **********.327972, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::6efe2c37a540485ed4ad0a3be41e6665", "start": **********.330032, "relative_start": 1.1432580947875977, "end": **********.330032, "relative_end": **********.330032, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::6efe2c37a540485ed4ad0a3be41e6665", "start": **********.332371, "relative_start": 1.145596981048584, "end": **********.332371, "relative_end": **********.332371, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::6efe2c37a540485ed4ad0a3be41e6665", "start": **********.335281, "relative_start": 1.1485068798065186, "end": **********.335281, "relative_end": **********.335281, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::6efe2c37a540485ed4ad0a3be41e6665", "start": **********.338381, "relative_start": 1.1516070365905762, "end": **********.338381, "relative_end": **********.338381, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::a460cf1eef36b568db70ad3c1eead84e", "start": **********.342531, "relative_start": 1.155756950378418, "end": **********.342531, "relative_end": **********.342531, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::6efe2c37a540485ed4ad0a3be41e6665", "start": **********.345495, "relative_start": 1.1587209701538086, "end": **********.345495, "relative_end": **********.345495, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::6efe2c37a540485ed4ad0a3be41e6665", "start": **********.348388, "relative_start": 1.161613941192627, "end": **********.348388, "relative_end": **********.348388, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::6efe2c37a540485ed4ad0a3be41e6665", "start": **********.351172, "relative_start": 1.164397954940796, "end": **********.351172, "relative_end": **********.351172, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::6efe2c37a540485ed4ad0a3be41e6665", "start": **********.353732, "relative_start": 1.1669580936431885, "end": **********.353732, "relative_end": **********.353732, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::6efe2c37a540485ed4ad0a3be41e6665", "start": **********.356298, "relative_start": 1.1695239543914795, "end": **********.356298, "relative_end": **********.356298, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::6efe2c37a540485ed4ad0a3be41e6665", "start": **********.359101, "relative_start": 1.1723270416259766, "end": **********.359101, "relative_end": **********.359101, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::a460cf1eef36b568db70ad3c1eead84e", "start": **********.361571, "relative_start": 1.1747970581054688, "end": **********.361571, "relative_end": **********.361571, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::6efe2c37a540485ed4ad0a3be41e6665", "start": **********.363987, "relative_start": 1.1772129535675049, "end": **********.363987, "relative_end": **********.363987, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::6efe2c37a540485ed4ad0a3be41e6665", "start": **********.366255, "relative_start": 1.179481029510498, "end": **********.366255, "relative_end": **********.366255, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::6efe2c37a540485ed4ad0a3be41e6665", "start": **********.368468, "relative_start": 1.1816940307617188, "end": **********.368468, "relative_end": **********.368468, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::6efe2c37a540485ed4ad0a3be41e6665", "start": **********.370877, "relative_start": 1.184103012084961, "end": **********.370877, "relative_end": **********.370877, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::6efe2c37a540485ed4ad0a3be41e6665", "start": **********.37368, "relative_start": 1.186906099319458, "end": **********.37368, "relative_end": **********.37368, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::6efe2c37a540485ed4ad0a3be41e6665", "start": **********.376428, "relative_start": 1.1896538734436035, "end": **********.376428, "relative_end": **********.376428, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::6efe2c37a540485ed4ad0a3be41e6665", "start": **********.378734, "relative_start": 1.191960096359253, "end": **********.378734, "relative_end": **********.378734, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::6efe2c37a540485ed4ad0a3be41e6665", "start": **********.380959, "relative_start": 1.1941850185394287, "end": **********.380959, "relative_end": **********.380959, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::6efe2c37a540485ed4ad0a3be41e6665", "start": **********.383144, "relative_start": 1.1963698863983154, "end": **********.383144, "relative_end": **********.383144, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::a460cf1eef36b568db70ad3c1eead84e", "start": **********.385472, "relative_start": 1.1986980438232422, "end": **********.385472, "relative_end": **********.385472, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::6efe2c37a540485ed4ad0a3be41e6665", "start": **********.387689, "relative_start": 1.2009150981903076, "end": **********.387689, "relative_end": **********.387689, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::6efe2c37a540485ed4ad0a3be41e6665", "start": **********.390183, "relative_start": 1.20340895652771, "end": **********.390183, "relative_end": **********.390183, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::a460cf1eef36b568db70ad3c1eead84e", "start": **********.392288, "relative_start": 1.2055139541625977, "end": **********.392288, "relative_end": **********.392288, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::6efe2c37a540485ed4ad0a3be41e6665", "start": **********.39455, "relative_start": 1.2077760696411133, "end": **********.39455, "relative_end": **********.39455, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::6efe2c37a540485ed4ad0a3be41e6665", "start": **********.396641, "relative_start": 1.209867000579834, "end": **********.396641, "relative_end": **********.396641, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "Preparing Response", "start": **********.398688, "relative_start": 1.2119140625, "end": **********.399286, "relative_end": **********.399286, "duration": 0.0005979537963867188, "duration_str": "598μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": 54043720, "peak_usage_str": "52MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "11.x", "tooltip": {"Laravel Version": "11.44.2", "PHP Version": "8.3.17", "Environment": "localhost", "Debug Mode": "Enabled", "URL": "muhrak.gc", "Timezone": "UTC", "Locale": "en"}}, "views": {"count": 41, "nb_templates": 41, "templates": [{"name": "__components::9a1da6c7f662474948fe63691d3a1543", "param_count": null, "params": [], "start": **********.966809, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\storage\\framework\\views/9a1da6c7f662474948fe63691d3a1543.blade.php__components::9a1da6c7f662474948fe63691d3a1543", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fstorage%2Fframework%2Fviews%2F9a1da6c7f662474948fe63691d3a1543.blade.php&line=1", "ajax": false, "filename": "9a1da6c7f662474948fe63691d3a1543.blade.php", "line": "?"}}, {"name": "__components::a460cf1eef36b568db70ad3c1eead84e", "param_count": null, "params": [], "start": **********.290499, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\storage\\framework\\views/a460cf1eef36b568db70ad3c1eead84e.blade.php__components::a460cf1eef36b568db70ad3c1eead84e", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fstorage%2Fframework%2Fviews%2Fa460cf1eef36b568db70ad3c1eead84e.blade.php&line=1", "ajax": false, "filename": "a460cf1eef36b568db70ad3c1eead84e.blade.php", "line": "?"}}, {"name": "__components::6efe2c37a540485ed4ad0a3be41e6665", "param_count": null, "params": [], "start": **********.301801, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\storage\\framework\\views/6efe2c37a540485ed4ad0a3be41e6665.blade.php__components::6efe2c37a540485ed4ad0a3be41e6665", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fstorage%2Fframework%2Fviews%2F6efe2c37a540485ed4ad0a3be41e6665.blade.php&line=1", "ajax": false, "filename": "6efe2c37a540485ed4ad0a3be41e6665.blade.php", "line": "?"}}, {"name": "__components::6efe2c37a540485ed4ad0a3be41e6665", "param_count": null, "params": [], "start": **********.304076, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\storage\\framework\\views/6efe2c37a540485ed4ad0a3be41e6665.blade.php__components::6efe2c37a540485ed4ad0a3be41e6665", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fstorage%2Fframework%2Fviews%2F6efe2c37a540485ed4ad0a3be41e6665.blade.php&line=1", "ajax": false, "filename": "6efe2c37a540485ed4ad0a3be41e6665.blade.php", "line": "?"}}, {"name": "__components::6efe2c37a540485ed4ad0a3be41e6665", "param_count": null, "params": [], "start": **********.306136, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\storage\\framework\\views/6efe2c37a540485ed4ad0a3be41e6665.blade.php__components::6efe2c37a540485ed4ad0a3be41e6665", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fstorage%2Fframework%2Fviews%2F6efe2c37a540485ed4ad0a3be41e6665.blade.php&line=1", "ajax": false, "filename": "6efe2c37a540485ed4ad0a3be41e6665.blade.php", "line": "?"}}, {"name": "__components::6efe2c37a540485ed4ad0a3be41e6665", "param_count": null, "params": [], "start": **********.308522, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\storage\\framework\\views/6efe2c37a540485ed4ad0a3be41e6665.blade.php__components::6efe2c37a540485ed4ad0a3be41e6665", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fstorage%2Fframework%2Fviews%2F6efe2c37a540485ed4ad0a3be41e6665.blade.php&line=1", "ajax": false, "filename": "6efe2c37a540485ed4ad0a3be41e6665.blade.php", "line": "?"}}, {"name": "__components::6efe2c37a540485ed4ad0a3be41e6665", "param_count": null, "params": [], "start": **********.311241, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\storage\\framework\\views/6efe2c37a540485ed4ad0a3be41e6665.blade.php__components::6efe2c37a540485ed4ad0a3be41e6665", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fstorage%2Fframework%2Fviews%2F6efe2c37a540485ed4ad0a3be41e6665.blade.php&line=1", "ajax": false, "filename": "6efe2c37a540485ed4ad0a3be41e6665.blade.php", "line": "?"}}, {"name": "__components::6efe2c37a540485ed4ad0a3be41e6665", "param_count": null, "params": [], "start": **********.313812, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\storage\\framework\\views/6efe2c37a540485ed4ad0a3be41e6665.blade.php__components::6efe2c37a540485ed4ad0a3be41e6665", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fstorage%2Fframework%2Fviews%2F6efe2c37a540485ed4ad0a3be41e6665.blade.php&line=1", "ajax": false, "filename": "6efe2c37a540485ed4ad0a3be41e6665.blade.php", "line": "?"}}, {"name": "__components::6efe2c37a540485ed4ad0a3be41e6665", "param_count": null, "params": [], "start": **********.316231, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\storage\\framework\\views/6efe2c37a540485ed4ad0a3be41e6665.blade.php__components::6efe2c37a540485ed4ad0a3be41e6665", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fstorage%2Fframework%2Fviews%2F6efe2c37a540485ed4ad0a3be41e6665.blade.php&line=1", "ajax": false, "filename": "6efe2c37a540485ed4ad0a3be41e6665.blade.php", "line": "?"}}, {"name": "__components::6efe2c37a540485ed4ad0a3be41e6665", "param_count": null, "params": [], "start": **********.318341, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\storage\\framework\\views/6efe2c37a540485ed4ad0a3be41e6665.blade.php__components::6efe2c37a540485ed4ad0a3be41e6665", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fstorage%2Fframework%2Fviews%2F6efe2c37a540485ed4ad0a3be41e6665.blade.php&line=1", "ajax": false, "filename": "6efe2c37a540485ed4ad0a3be41e6665.blade.php", "line": "?"}}, {"name": "__components::a460cf1eef36b568db70ad3c1eead84e", "param_count": null, "params": [], "start": **********.320534, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\storage\\framework\\views/a460cf1eef36b568db70ad3c1eead84e.blade.php__components::a460cf1eef36b568db70ad3c1eead84e", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fstorage%2Fframework%2Fviews%2Fa460cf1eef36b568db70ad3c1eead84e.blade.php&line=1", "ajax": false, "filename": "a460cf1eef36b568db70ad3c1eead84e.blade.php", "line": "?"}}, {"name": "__components::6efe2c37a540485ed4ad0a3be41e6665", "param_count": null, "params": [], "start": **********.323112, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\storage\\framework\\views/6efe2c37a540485ed4ad0a3be41e6665.blade.php__components::6efe2c37a540485ed4ad0a3be41e6665", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fstorage%2Fframework%2Fviews%2F6efe2c37a540485ed4ad0a3be41e6665.blade.php&line=1", "ajax": false, "filename": "6efe2c37a540485ed4ad0a3be41e6665.blade.php", "line": "?"}}, {"name": "__components::6efe2c37a540485ed4ad0a3be41e6665", "param_count": null, "params": [], "start": **********.325722, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\storage\\framework\\views/6efe2c37a540485ed4ad0a3be41e6665.blade.php__components::6efe2c37a540485ed4ad0a3be41e6665", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fstorage%2Fframework%2Fviews%2F6efe2c37a540485ed4ad0a3be41e6665.blade.php&line=1", "ajax": false, "filename": "6efe2c37a540485ed4ad0a3be41e6665.blade.php", "line": "?"}}, {"name": "__components::6efe2c37a540485ed4ad0a3be41e6665", "param_count": null, "params": [], "start": **********.327953, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\storage\\framework\\views/6efe2c37a540485ed4ad0a3be41e6665.blade.php__components::6efe2c37a540485ed4ad0a3be41e6665", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fstorage%2Fframework%2Fviews%2F6efe2c37a540485ed4ad0a3be41e6665.blade.php&line=1", "ajax": false, "filename": "6efe2c37a540485ed4ad0a3be41e6665.blade.php", "line": "?"}}, {"name": "__components::6efe2c37a540485ed4ad0a3be41e6665", "param_count": null, "params": [], "start": **********.330012, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\storage\\framework\\views/6efe2c37a540485ed4ad0a3be41e6665.blade.php__components::6efe2c37a540485ed4ad0a3be41e6665", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fstorage%2Fframework%2Fviews%2F6efe2c37a540485ed4ad0a3be41e6665.blade.php&line=1", "ajax": false, "filename": "6efe2c37a540485ed4ad0a3be41e6665.blade.php", "line": "?"}}, {"name": "__components::6efe2c37a540485ed4ad0a3be41e6665", "param_count": null, "params": [], "start": **********.332348, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\storage\\framework\\views/6efe2c37a540485ed4ad0a3be41e6665.blade.php__components::6efe2c37a540485ed4ad0a3be41e6665", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fstorage%2Fframework%2Fviews%2F6efe2c37a540485ed4ad0a3be41e6665.blade.php&line=1", "ajax": false, "filename": "6efe2c37a540485ed4ad0a3be41e6665.blade.php", "line": "?"}}, {"name": "__components::6efe2c37a540485ed4ad0a3be41e6665", "param_count": null, "params": [], "start": **********.335256, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\storage\\framework\\views/6efe2c37a540485ed4ad0a3be41e6665.blade.php__components::6efe2c37a540485ed4ad0a3be41e6665", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fstorage%2Fframework%2Fviews%2F6efe2c37a540485ed4ad0a3be41e6665.blade.php&line=1", "ajax": false, "filename": "6efe2c37a540485ed4ad0a3be41e6665.blade.php", "line": "?"}}, {"name": "__components::6efe2c37a540485ed4ad0a3be41e6665", "param_count": null, "params": [], "start": **********.338357, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\storage\\framework\\views/6efe2c37a540485ed4ad0a3be41e6665.blade.php__components::6efe2c37a540485ed4ad0a3be41e6665", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fstorage%2Fframework%2Fviews%2F6efe2c37a540485ed4ad0a3be41e6665.blade.php&line=1", "ajax": false, "filename": "6efe2c37a540485ed4ad0a3be41e6665.blade.php", "line": "?"}}, {"name": "__components::a460cf1eef36b568db70ad3c1eead84e", "param_count": null, "params": [], "start": **********.342494, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\storage\\framework\\views/a460cf1eef36b568db70ad3c1eead84e.blade.php__components::a460cf1eef36b568db70ad3c1eead84e", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fstorage%2Fframework%2Fviews%2Fa460cf1eef36b568db70ad3c1eead84e.blade.php&line=1", "ajax": false, "filename": "a460cf1eef36b568db70ad3c1eead84e.blade.php", "line": "?"}}, {"name": "__components::6efe2c37a540485ed4ad0a3be41e6665", "param_count": null, "params": [], "start": **********.345463, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\storage\\framework\\views/6efe2c37a540485ed4ad0a3be41e6665.blade.php__components::6efe2c37a540485ed4ad0a3be41e6665", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fstorage%2Fframework%2Fviews%2F6efe2c37a540485ed4ad0a3be41e6665.blade.php&line=1", "ajax": false, "filename": "6efe2c37a540485ed4ad0a3be41e6665.blade.php", "line": "?"}}, {"name": "__components::6efe2c37a540485ed4ad0a3be41e6665", "param_count": null, "params": [], "start": **********.348362, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\storage\\framework\\views/6efe2c37a540485ed4ad0a3be41e6665.blade.php__components::6efe2c37a540485ed4ad0a3be41e6665", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fstorage%2Fframework%2Fviews%2F6efe2c37a540485ed4ad0a3be41e6665.blade.php&line=1", "ajax": false, "filename": "6efe2c37a540485ed4ad0a3be41e6665.blade.php", "line": "?"}}, {"name": "__components::6efe2c37a540485ed4ad0a3be41e6665", "param_count": null, "params": [], "start": **********.351149, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\storage\\framework\\views/6efe2c37a540485ed4ad0a3be41e6665.blade.php__components::6efe2c37a540485ed4ad0a3be41e6665", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fstorage%2Fframework%2Fviews%2F6efe2c37a540485ed4ad0a3be41e6665.blade.php&line=1", "ajax": false, "filename": "6efe2c37a540485ed4ad0a3be41e6665.blade.php", "line": "?"}}, {"name": "__components::6efe2c37a540485ed4ad0a3be41e6665", "param_count": null, "params": [], "start": **********.353709, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\storage\\framework\\views/6efe2c37a540485ed4ad0a3be41e6665.blade.php__components::6efe2c37a540485ed4ad0a3be41e6665", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fstorage%2Fframework%2Fviews%2F6efe2c37a540485ed4ad0a3be41e6665.blade.php&line=1", "ajax": false, "filename": "6efe2c37a540485ed4ad0a3be41e6665.blade.php", "line": "?"}}, {"name": "__components::6efe2c37a540485ed4ad0a3be41e6665", "param_count": null, "params": [], "start": **********.356258, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\storage\\framework\\views/6efe2c37a540485ed4ad0a3be41e6665.blade.php__components::6efe2c37a540485ed4ad0a3be41e6665", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fstorage%2Fframework%2Fviews%2F6efe2c37a540485ed4ad0a3be41e6665.blade.php&line=1", "ajax": false, "filename": "6efe2c37a540485ed4ad0a3be41e6665.blade.php", "line": "?"}}, {"name": "__components::6efe2c37a540485ed4ad0a3be41e6665", "param_count": null, "params": [], "start": **********.359078, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\storage\\framework\\views/6efe2c37a540485ed4ad0a3be41e6665.blade.php__components::6efe2c37a540485ed4ad0a3be41e6665", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fstorage%2Fframework%2Fviews%2F6efe2c37a540485ed4ad0a3be41e6665.blade.php&line=1", "ajax": false, "filename": "6efe2c37a540485ed4ad0a3be41e6665.blade.php", "line": "?"}}, {"name": "__components::a460cf1eef36b568db70ad3c1eead84e", "param_count": null, "params": [], "start": **********.361549, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\storage\\framework\\views/a460cf1eef36b568db70ad3c1eead84e.blade.php__components::a460cf1eef36b568db70ad3c1eead84e", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fstorage%2Fframework%2Fviews%2Fa460cf1eef36b568db70ad3c1eead84e.blade.php&line=1", "ajax": false, "filename": "a460cf1eef36b568db70ad3c1eead84e.blade.php", "line": "?"}}, {"name": "__components::6efe2c37a540485ed4ad0a3be41e6665", "param_count": null, "params": [], "start": **********.363965, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\storage\\framework\\views/6efe2c37a540485ed4ad0a3be41e6665.blade.php__components::6efe2c37a540485ed4ad0a3be41e6665", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fstorage%2Fframework%2Fviews%2F6efe2c37a540485ed4ad0a3be41e6665.blade.php&line=1", "ajax": false, "filename": "6efe2c37a540485ed4ad0a3be41e6665.blade.php", "line": "?"}}, {"name": "__components::6efe2c37a540485ed4ad0a3be41e6665", "param_count": null, "params": [], "start": **********.366225, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\storage\\framework\\views/6efe2c37a540485ed4ad0a3be41e6665.blade.php__components::6efe2c37a540485ed4ad0a3be41e6665", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fstorage%2Fframework%2Fviews%2F6efe2c37a540485ed4ad0a3be41e6665.blade.php&line=1", "ajax": false, "filename": "6efe2c37a540485ed4ad0a3be41e6665.blade.php", "line": "?"}}, {"name": "__components::6efe2c37a540485ed4ad0a3be41e6665", "param_count": null, "params": [], "start": **********.368446, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\storage\\framework\\views/6efe2c37a540485ed4ad0a3be41e6665.blade.php__components::6efe2c37a540485ed4ad0a3be41e6665", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fstorage%2Fframework%2Fviews%2F6efe2c37a540485ed4ad0a3be41e6665.blade.php&line=1", "ajax": false, "filename": "6efe2c37a540485ed4ad0a3be41e6665.blade.php", "line": "?"}}, {"name": "__components::6efe2c37a540485ed4ad0a3be41e6665", "param_count": null, "params": [], "start": **********.370856, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\storage\\framework\\views/6efe2c37a540485ed4ad0a3be41e6665.blade.php__components::6efe2c37a540485ed4ad0a3be41e6665", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fstorage%2Fframework%2Fviews%2F6efe2c37a540485ed4ad0a3be41e6665.blade.php&line=1", "ajax": false, "filename": "6efe2c37a540485ed4ad0a3be41e6665.blade.php", "line": "?"}}, {"name": "__components::6efe2c37a540485ed4ad0a3be41e6665", "param_count": null, "params": [], "start": **********.373654, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\storage\\framework\\views/6efe2c37a540485ed4ad0a3be41e6665.blade.php__components::6efe2c37a540485ed4ad0a3be41e6665", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fstorage%2Fframework%2Fviews%2F6efe2c37a540485ed4ad0a3be41e6665.blade.php&line=1", "ajax": false, "filename": "6efe2c37a540485ed4ad0a3be41e6665.blade.php", "line": "?"}}, {"name": "__components::6efe2c37a540485ed4ad0a3be41e6665", "param_count": null, "params": [], "start": **********.376406, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\storage\\framework\\views/6efe2c37a540485ed4ad0a3be41e6665.blade.php__components::6efe2c37a540485ed4ad0a3be41e6665", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fstorage%2Fframework%2Fviews%2F6efe2c37a540485ed4ad0a3be41e6665.blade.php&line=1", "ajax": false, "filename": "6efe2c37a540485ed4ad0a3be41e6665.blade.php", "line": "?"}}, {"name": "__components::6efe2c37a540485ed4ad0a3be41e6665", "param_count": null, "params": [], "start": **********.378712, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\storage\\framework\\views/6efe2c37a540485ed4ad0a3be41e6665.blade.php__components::6efe2c37a540485ed4ad0a3be41e6665", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fstorage%2Fframework%2Fviews%2F6efe2c37a540485ed4ad0a3be41e6665.blade.php&line=1", "ajax": false, "filename": "6efe2c37a540485ed4ad0a3be41e6665.blade.php", "line": "?"}}, {"name": "__components::6efe2c37a540485ed4ad0a3be41e6665", "param_count": null, "params": [], "start": **********.380923, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\storage\\framework\\views/6efe2c37a540485ed4ad0a3be41e6665.blade.php__components::6efe2c37a540485ed4ad0a3be41e6665", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fstorage%2Fframework%2Fviews%2F6efe2c37a540485ed4ad0a3be41e6665.blade.php&line=1", "ajax": false, "filename": "6efe2c37a540485ed4ad0a3be41e6665.blade.php", "line": "?"}}, {"name": "__components::6efe2c37a540485ed4ad0a3be41e6665", "param_count": null, "params": [], "start": **********.383123, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\storage\\framework\\views/6efe2c37a540485ed4ad0a3be41e6665.blade.php__components::6efe2c37a540485ed4ad0a3be41e6665", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fstorage%2Fframework%2Fviews%2F6efe2c37a540485ed4ad0a3be41e6665.blade.php&line=1", "ajax": false, "filename": "6efe2c37a540485ed4ad0a3be41e6665.blade.php", "line": "?"}}, {"name": "__components::a460cf1eef36b568db70ad3c1eead84e", "param_count": null, "params": [], "start": **********.38545, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\storage\\framework\\views/a460cf1eef36b568db70ad3c1eead84e.blade.php__components::a460cf1eef36b568db70ad3c1eead84e", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fstorage%2Fframework%2Fviews%2Fa460cf1eef36b568db70ad3c1eead84e.blade.php&line=1", "ajax": false, "filename": "a460cf1eef36b568db70ad3c1eead84e.blade.php", "line": "?"}}, {"name": "__components::6efe2c37a540485ed4ad0a3be41e6665", "param_count": null, "params": [], "start": **********.387668, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\storage\\framework\\views/6efe2c37a540485ed4ad0a3be41e6665.blade.php__components::6efe2c37a540485ed4ad0a3be41e6665", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fstorage%2Fframework%2Fviews%2F6efe2c37a540485ed4ad0a3be41e6665.blade.php&line=1", "ajax": false, "filename": "6efe2c37a540485ed4ad0a3be41e6665.blade.php", "line": "?"}}, {"name": "__components::6efe2c37a540485ed4ad0a3be41e6665", "param_count": null, "params": [], "start": **********.390163, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\storage\\framework\\views/6efe2c37a540485ed4ad0a3be41e6665.blade.php__components::6efe2c37a540485ed4ad0a3be41e6665", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fstorage%2Fframework%2Fviews%2F6efe2c37a540485ed4ad0a3be41e6665.blade.php&line=1", "ajax": false, "filename": "6efe2c37a540485ed4ad0a3be41e6665.blade.php", "line": "?"}}, {"name": "__components::a460cf1eef36b568db70ad3c1eead84e", "param_count": null, "params": [], "start": **********.392268, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\storage\\framework\\views/a460cf1eef36b568db70ad3c1eead84e.blade.php__components::a460cf1eef36b568db70ad3c1eead84e", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fstorage%2Fframework%2Fviews%2Fa460cf1eef36b568db70ad3c1eead84e.blade.php&line=1", "ajax": false, "filename": "a460cf1eef36b568db70ad3c1eead84e.blade.php", "line": "?"}}, {"name": "__components::6efe2c37a540485ed4ad0a3be41e6665", "param_count": null, "params": [], "start": **********.394529, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\storage\\framework\\views/6efe2c37a540485ed4ad0a3be41e6665.blade.php__components::6efe2c37a540485ed4ad0a3be41e6665", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fstorage%2Fframework%2Fviews%2F6efe2c37a540485ed4ad0a3be41e6665.blade.php&line=1", "ajax": false, "filename": "6efe2c37a540485ed4ad0a3be41e6665.blade.php", "line": "?"}}, {"name": "__components::6efe2c37a540485ed4ad0a3be41e6665", "param_count": null, "params": [], "start": **********.396622, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\storage\\framework\\views/6efe2c37a540485ed4ad0a3be41e6665.blade.php__components::6efe2c37a540485ed4ad0a3be41e6665", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fstorage%2Fframework%2Fviews%2F6efe2c37a540485ed4ad0a3be41e6665.blade.php&line=1", "ajax": false, "filename": "6efe2c37a540485ed4ad0a3be41e6665.blade.php", "line": "?"}}]}, "queries": {"count": 5, "nb_statements": 5, "nb_visible_statements": 5, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.29641999999999996, "accumulated_duration_str": "296ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 170}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}], "start": **********.920484, "duration": 0.00051, "duration_str": "510μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 0, "width_percent": 0.172}, {"sql": "select * from `user_meta` where `user_meta`.`user_id` = 1 and `user_meta`.`user_id` is not null", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "platform/core/acl/src/Concerns/HasPreferences.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\acl\\src\\Concerns\\HasPreferences.php", "line": 62}, {"index": 18, "namespace": null, "name": "platform/core/acl/src/Concerns/HasPreferences.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\acl\\src\\Concerns\\HasPreferences.php", "line": 32}, {"index": 19, "namespace": null, "name": "platform/core/base/src/Supports/AdminAppearance.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Supports\\AdminAppearance.php", "line": 99}, {"index": 20, "namespace": null, "name": "platform/core/base/src/Supports/AdminAppearance.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Supports\\AdminAppearance.php", "line": 39}], "start": **********.929363, "duration": 0.00046, "duration_str": "460μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 0.172, "width_percent": 0.155}, {"sql": "select `lang_locale`, `lang_code`, `lang_name`, `lang_flag`, `lang_is_rtl` from `languages` order by `lang_order` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 16, "namespace": null, "name": "platform/plugins/language/src/LanguageManager.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\language\\src\\LanguageManager.php", "line": 156}, {"index": 18, "namespace": null, "name": "platform/plugins/language/src/Providers/HookServiceProvider.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\language\\src\\Providers\\HookServiceProvider.php", "line": 105}, {"index": 22, "namespace": null, "name": "platform/core/base/helpers/action-filter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\helpers\\action-filter.php", "line": 39}, {"index": 24, "namespace": null, "name": "platform/core/base/src/Http/Middleware/AdminLocaleMiddleware.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Http\\Middleware\\AdminLocaleMiddleware.php", "line": 28}], "start": **********.9373262, "duration": 0.00045, "duration_str": "450μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 0.327, "width_percent": 0.152}, {"sql": "(select `media_files`.`id` as `id`, `media_files`.`name` as `name`, `media_files`.`alt` as `alt`, `media_files`.`url` as `url`, `media_files`.`mime_type` as `mime_type`, `media_files`.`size` as `size`, `media_files`.`created_at` as `created_at`, `media_files`.`updated_at` as `updated_at`, `media_files`.`options` as `options`, `media_files`.`folder_id` as `folder_id`, `media_files`.`visibility` as `visibility`, 0 as is_folder, NULL as slug, NULL as parent_id, NULL as color from `media_files` left join `media_folders` on `media_folders`.`id` = `media_files`.`folder_id` where ((`media_files`.`folder_id` = '0' and `media_files`.`deleted_at` is null) or (`media_files`.`deleted_at` is null and `media_folders`.`deleted_at` is not null) or (`media_files`.`deleted_at` is null and `media_folders`.`id` is null))) union (select `media_folders`.`id` as `id`, `media_folders`.`name` as `name`, NULL as url, NULL as mime_type, NULL as size, NULL as alt, `media_folders`.`created_at` as `created_at`, `media_folders`.`updated_at` as `updated_at`, NULL as options, NULL as folder_id, NULL as visibility, 1 as is_folder, `media_folders`.`slug` as `slug`, `media_folders`.`parent_id` as `parent_id`, `media_folders`.`color` as `color` from `media_folders` where `parent_id` = '0' and `media_folders`.`deleted_at` is null) order by `is_folder` asc, `created_at` desc limit 40 offset 0", "type": "query", "params": [], "bindings": ["0", "0"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 16, "namespace": null, "name": "platform/core/media/src/Repositories/Eloquent/MediaFileRepository.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\media\\src\\Repositories\\Eloquent\\MediaFileRepository.php", "line": 225}, {"index": 17, "namespace": null, "name": "platform/core/media/src/Repositories/Eloquent/MediaFileRepository.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\media\\src\\Repositories\\Eloquent\\MediaFileRepository.php", "line": 154}, {"index": 18, "namespace": null, "name": "platform/core/media/src/Http/Controllers/MediaController.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\media\\src\\Http\\Controllers\\MediaController.php", "line": 121}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.968703, "duration": 0.29446, "duration_str": "294ms", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 0.479, "width_percent": 99.339}, {"sql": "select `media_files`.`id` as `id`, `media_files`.`name` as `name`, `media_files`.`alt` as `alt`, `media_files`.`url` as `url`, `media_files`.`mime_type` as `mime_type`, `media_files`.`size` as `size`, `media_files`.`created_at` as `created_at`, `media_files`.`updated_at` as `updated_at`, `media_files`.`options` as `options`, `media_files`.`folder_id` as `folder_id`, `media_files`.`visibility` as `visibility`, 0 as is_folder, NULL as slug, NULL as parent_id, NULL as color from `media_files` where `id` is null and `media_files`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "platform/core/media/src/Repositories/Eloquent/MediaFileRepository.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\media\\src\\Repositories\\Eloquent\\MediaFileRepository.php", "line": 238}, {"index": 18, "namespace": null, "name": "platform/core/media/src/Repositories/Eloquent/MediaFileRepository.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\media\\src\\Repositories\\Eloquent\\MediaFileRepository.php", "line": 154}, {"index": 19, "namespace": null, "name": "platform/core/media/src/Http/Controllers/MediaController.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\media\\src\\Http\\Controllers\\MediaController.php", "line": 121}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.266007, "duration": 0.00054, "duration_str": "540μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 99.818, "width_percent": 0.182}]}, "models": {"data": {"Botble\\Media\\Models\\MediaFile": {"value": 40, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fmedia%2Fsrc%2FModels%2FMediaFile.php&line=1", "ajax": false, "filename": "MediaFile.php", "line": "?"}}, "Botble\\Language\\Models\\Language": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Flanguage%2Fsrc%2FModels%2FLanguage.php&line=1", "ajax": false, "filename": "Language.php", "line": "?"}}, "Botble\\ACL\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Facl%2Fsrc%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Botble\\ACL\\Models\\UserMeta": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Facl%2Fsrc%2FModels%2FUserMeta.php&line=1", "ajax": false, "filename": "UserMeta.php", "line": "?"}}}, "count": 44, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "request": {"data": {"status": "200 OK", "full_url": "https://muhrak.gc/admin/media/list?filter=everything&folder_id=0&load_more_file=false&multiple=false...", "action_name": "media.list", "controller_action": "Botble\\Media\\Http\\Controllers\\MediaController@getList", "uri": "GET admin/media/list", "permission": "media.index", "controller": "Botble\\Media\\Http\\Controllers\\MediaController@getList<a href=\"phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fmedia%2Fsrc%2FHttp%2FControllers%2FMediaController.php&line=57\" class=\"phpdebugbar-widgets-editor-link\"></a>", "namespace": "Botble\\Media\\Http\\Controllers", "prefix": "admin/media", "file": "<a href=\"phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fmedia%2Fsrc%2FHttp%2FControllers%2FMediaController.php&line=57\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">platform/core/media/src/Http/Controllers/MediaController.php:57-236</a>", "middleware": "web, core, auth", "duration": "1.21s", "peak_memory": "52MB", "response": "application/json", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-1039388299 data-indent-pad=\"  \"><span class=sf-dump-note>array:12</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>view_type</span>\" => \"<span class=sf-dump-str title=\"5 characters\">tiles</span>\"\n  \"<span class=sf-dump-key>filter</span>\" => \"<span class=sf-dump-str title=\"10 characters\">everything</span>\"\n  \"<span class=sf-dump-key>view_in</span>\" => \"<span class=sf-dump-str title=\"9 characters\">all_media</span>\"\n  \"<span class=sf-dump-key>sort_by</span>\" => \"<span class=sf-dump-str title=\"15 characters\">created_at-desc</span>\"\n  \"<span class=sf-dump-key>folder_id</span>\" => \"<span class=sf-dump-str>0</span>\"\n  \"<span class=sf-dump-key>search</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>multiple</span>\" => \"<span class=sf-dump-str title=\"5 characters\">false</span>\"\n  \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str>*</span>\"\n  \"<span class=sf-dump-key>open_in</span>\" => \"<span class=sf-dump-str title=\"5 characters\">modal</span>\"\n  \"<span class=sf-dump-key>load_more_file</span>\" => \"<span class=sf-dump-str title=\"5 characters\">false</span>\"\n  \"<span class=sf-dump-key>paged</span>\" => \"<span class=sf-dump-str>1</span>\"\n  \"<span class=sf-dump-key>posts_per_page</span>\" => \"<span class=sf-dump-str title=\"2 characters\">40</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1039388299\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1673188572 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1673188572\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-86032429 data-indent-pad=\"  \"><span class=sf-dump-note>array:19</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>x-xsrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"340 characters\">eyJpdiI6IkNQVC9heVZ4QXFKdG1jc3ZnVTVuL2c9PSIsInZhbHVlIjoiQnhiOGJtMDBrYldaMUhEZEsrYk1iOGNvNlpzVWtlYzlGNE9YMkhOKzB6Wm9qU2R2MFliVUFMZHVSK25zUlZqK2ZzZzZXZjNXRkVQSnlDUVZnZlR1YnpmSVlBZWl4Mk8ycnR1QmVSQUloVk5ucEphODZMNjUzWHUyeHVZUGJHeUsiLCJtYWMiOiI0NGE3NGEyZWFlYmI3OTgxYjdiMTlmYTIwZDVjZWYxYjIwNzMyN2UxYWQzMzBhZjNlNTg4MjBjOTBlYTAzZTBmIiwidGFnIjoiIn0=</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">muhrak.gc</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pragma</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"33 characters\">application/json, text/plain, */*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>dnt</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"51 characters\">https://muhrak.gc/admin/marketplaces/stores/edit/89</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"50 characters\">en-US,en;q=0.9,ur;q=0.8,pl;q=0.7,ru;q=0.6,ar;q=0.5</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3140 characters\">cookie_for_consent=1; botble_footprints_cookie=eyJpdiI6InJtenpwcG01ZldyRCs2TFV4YUYrYUE9PSIsInZhbHVlIjoiamVKQ2MwT2VxdDI3L0pXTXdlMURsY0JJSHdaWVp5T1VoSUNSbEpIZ0pZcHdKWGZId3lBLzJ2b1pIUXVYUUxKRjdMY3k5Q1VpTWRFd3Ftbzd6L3UyeHUvdWFpdTAwUXBNZER4MDc5MGJjOXQxQXhIazNiaTI0TU42YXJkbHNVek8iLCJtYWMiOiI3YWM1Njc0ODBhMzFhMjBlOWM2NzQ2YTE1ODRkNGM0ODk5YTNhYjE5ZjliNjY3NGI2MzA5MjMxNGJhNDliZjJjIiwidGFnIjoiIn0%3D; botble_footprints_cookie_data=eyJpdiI6ImozUjJ6OXN5RkhxbXdCYmc5K1JURlE9PSIsInZhbHVlIjoib3plTVp0NnFEZVhoU1NQejZBMklhY2RqSEtwTmNvaG1SeWtpbFkvZmVZb21QUmg4VTFvRVlPbUUzd1NGQ3J6dFZVT0hmdDIrTWd0SGF1Y28yYWx1VWJYdTJ0OUFvdkxJM3U3UktZNXpBL1pReU8xcDI0S3AxSmRkVzVHaHhyZHlia1ptb0wrTUhkNHRPbXhoY2pNL2JoZ2JXVGNTaHI2VjRIRW1HOUdiVHAwN2xxdkJLazdwZDFadCtJaTNlTk9HT2JmWms2Vnp5bGorWkxFd1dvaStjUHlEY1ZQR2tYOW5tREUxV25Vek1jbDFkenJlZENXR3VIaHM4aFdLaDRVNjViY21idXByU3ZQQVFCU2gxcVNnYW5zbERUamgxZjNUMVRIWEt4V0tUbGxFRDc5VzhDNkZRZ0g1QXRjU3Rna3ZTQlRwZjVScFM1eEdzUmNhSlAxaHpIZ2kxdVEvOG51K1FlVmhuSTBGdUZxYXdkdGdWLzFDdW1nZ0MvdDFnTit6STdZd3JJWnVCNGZZSTNFRWtjaTJndzZVT3prNEkrb1c0cTFFaGFWckxiUUtVSFVsYXlWSWxnUSttR2dxYlo0K0dCY1NzRDBGdzVNZGdLTnFOZG43VllRTkFORnIrZlk1SEt3ZFkvNjYwdjJFWmFJaCtPUVh4TjFZejFleUQvU09oeERvVkdyVVJ2d0ZkM25TZXU1OGNZOE5PWTJWVmV4WWpsWUE2Zmc0dHV3PSIsIm1hYyI6IjYwMTc0ODhiZDNjN2VkZGVmZjliNmYyNWU0ZTU2ZWE5YjhkMjllNzk1ZDAxNGU2ZWU3MDhkOTA3NzM3OTFjNjUiLCJ0YWciOiIifQ%3D%3D; remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6IktsRlR4UnBrM0szS1Y0SUxrVVlUdHc9PSIsInZhbHVlIjoibVgzWjBpSHR4T2o4NlJBc3ZWNEVGZGVXS0xzU3NlNjRPOXFyNVFIOVVkbCt1ODRxbVZFNkNaODlSa3BORzF3bTJEb3Rkb0RVTVVjZHNBVFZiVnQ2TlhLOVBNVkJhVUIzb0ZZV2VTOER2dEJjd1ZZZEw3OWg4UEV4OXZqSjQwR2hsbERhOTdCQ1puTTJGRG1nMnozejdpcXp0NllsWUVzYWl5SjFlZGhYVWdUNmYyY2xRVXdXeG9UU3M3S1kxMnVxbVc5bktlTHFEOHNJbDlUV2JPamxrK3AwangreXlUMmtBTVhRdlN0SHQwcz0iLCJtYWMiOiIzNTliMzJjOTQxODc2NDdkNDI0YTBiNjZkMzUzZjEzNzZjYWMwYzlmNDQ2NGZhZjJiOTk5M2NhYTc0YmEyOGY5IiwidGFnIjoiIn0%3D; remember_customer_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6Ijl6MUxxZXpoME96cm9sVGNGeUlyaEE9PSIsInZhbHVlIjoiWlR1dnBBRnpBZWF2c1I3dndid2M5UllhSUVPN0Fqa1p0K0RGelpiVkRpditYckx0YWZ2RVZFbTMwUGVnVGh4ODc5VFBseUtFaytWNWpRalB1QVhJbHZONFhqaFc2UjdjTlI1SlR0TDAwMS9ldmZxN1F5aTZpaWdnaFNOaGs0VmRWMlBoNkhMeTZKSU1hVTA3UHRKNUJKb3hPUEJVUWtEcjc2RFZmQ3NVanJEeHNHMU1jc3QzQ2N5blJKUUJVaFNsUEFQSmVKSHBiTjhtVmU2OEUwY1dBMTNyblVPOENzcTlRcHBOOTVuTWc1OD0iLCJtYWMiOiJhNzJhNzEyNGRjOTM0ZmVhMTIyNDU0ZmM0Mzc2MDQzZjI3MjNjNmM4NTUwMGQ2ZTZlNmZjM2NlMWZkYTgyMGM0IiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6IkNQVC9heVZ4QXFKdG1jc3ZnVTVuL2c9PSIsInZhbHVlIjoiQnhiOGJtMDBrYldaMUhEZEsrYk1iOGNvNlpzVWtlYzlGNE9YMkhOKzB6Wm9qU2R2MFliVUFMZHVSK25zUlZqK2ZzZzZXZjNXRkVQSnlDUVZnZlR1YnpmSVlBZWl4Mk8ycnR1QmVSQUloVk5ucEphODZMNjUzWHUyeHVZUGJHeUsiLCJtYWMiOiI0NGE3NGEyZWFlYmI3OTgxYjdiMTlmYTIwZDVjZWYxYjIwNzMyN2UxYWQzMzBhZjNlNTg4MjBjOTBlYTAzZTBmIiwidGFnIjoiIn0%3D; botble_session=eyJpdiI6InhmcVdRWHVTZUNOd2tGdmdTdXQrR3c9PSIsInZhbHVlIjoiOWJFRzdSQnJOMklFWVNTTXRaWkk0aXVXQ1BwQVFDQjAxeVVzVnQ5YUtBY0tOWDlEZXVMYzNsMjcvVDdyMEd1VU1hY2tDN20ybG1jRGg1YktYTmk3dUIxazdDcWNVd0xndEM1V1pHb0hUS3dsTC94Vk9LZDFoRlJiUGRqOUJ2RnoiLCJtYWMiOiIwN2VlNjY1MDA5ZGRjZmE1ZDcxOTZiNGJhNTU4MTRkN2IzZWY3NzFmOWU2OGUxMzgwMzdmNGY1NDUyMGZlNDkzIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-86032429\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-447465499 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cookie_for_consent</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>botble_footprints_cookie</span>\" => \"<span class=sf-dump-str title=\"40 characters\">14908b951300852119d2c46221b4b11a7e2b8107</span>\"\n  \"<span class=sf-dump-key>botble_footprints_cookie_data</span>\" => \"<span class=sf-dump-str title=\"359 characters\">{&quot;footprint&quot;:&quot;14908b951300852119d2c46221b4b11a7e2b8107&quot;,&quot;ip&quot;:&quot;127.0.0.1&quot;,&quot;landing_domain&quot;:&quot;muhrak.gc&quot;,&quot;landing_page&quot;:&quot;install\\/welcome&quot;,&quot;landing_params&quot;:null,&quot;referral&quot;:null,&quot;gclid&quot;:null,&quot;fclid&quot;:null,&quot;utm_source&quot;:null,&quot;utm_campaign&quot;:null,&quot;utm_medium&quot;:null,&quot;utm_term&quot;:null,&quot;utm_content&quot;:null,&quot;referrer_url&quot;:&quot;http:\\/\\/localhost\\/&quot;,&quot;referrer_domain&quot;:&quot;localhost&quot;}</span>\"\n  \"<span class=sf-dump-key>remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => \"<span class=sf-dump-str title=\"123 characters\">1|lEoydfVrZKIFerelBjYotnW4P2e0TuoqJSFCOOr89XMprC6ygbibdEjl3rvX|$2y$12$6oEMzkNhsgbeo4WDg72G9e2nWxuvMvozEUdkIQ0AfpRz3PJNQ7XzG</span>\"\n  \"<span class=sf-dump-key>remember_customer_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => \"<span class=sf-dump-str title=\"123 characters\">2|eZ0awTe8bfQLceQTos5gvZ7R55kW3IiLBR997mfYDDJcPndE2TcWxwpqf39P|$2y$12$EU5h35Jju9igRCRES6rqCuE42l30uwJ94rSesc56rboRI0xQqYy/G</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">56w3qOLQ2WezgBAffsP49hPOj3d80BP8siOqKnfZ</span>\"\n  \"<span class=sf-dump-key>botble_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">NlmohzskXFfa1956yAEyoTgVOMjJqryOKOFtoCn1</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-447465499\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 13 Jun 2025 22:14:42 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-279466921 data-indent-pad=\"  \"><span class=sf-dump-note>array:10</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">56w3qOLQ2WezgBAffsP49hPOj3d80BP8siOqKnfZ</span>\"\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>language</span>\" => \"<span class=sf-dump-str title=\"2 characters\">en</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"51 characters\">https://muhrak.gc/admin/marketplaces/stores/edit/89</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>login_customer_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>2</span>\n  \"<span class=sf-dump-key>locale_direction</span>\" => <span class=sf-dump-const>false</span>\n  \"<span class=sf-dump-key>viewed_product</span>\" => <span class=sf-dump-note>array:7</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-key>10811</span> => <span class=sf-dump-num>1749849428</span>\n    <span class=sf-dump-key>129</span> => <span class=sf-dump-num>1749849548</span>\n    <span class=sf-dump-key>119</span> => <span class=sf-dump-num>1749849650</span>\n    <span class=sf-dump-key>277</span> => <span class=sf-dump-num>1749849868</span>\n    <span class=sf-dump-key>115</span> => <span class=sf-dump-num>1749849904</span>\n    <span class=sf-dump-key>253</span> => <span class=sf-dump-num>1749850000</span>\n    <span class=sf-dump-key>989</span> => <span class=sf-dump-num>1749850593</span>\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-279466921\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "https://muhrak.gc/admin/media/list?filter=everything&folder_id=0&load_more_file=false&multiple=false...", "action_name": "media.list", "controller_action": "Botble\\Media\\Http\\Controllers\\MediaController@getList"}, "badge": null}}