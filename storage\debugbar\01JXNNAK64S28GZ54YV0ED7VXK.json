{"__meta": {"id": "01JXNNAK64S28GZ54YV0ED7VXK", "datetime": "2025-06-13 22:07:21", "utime": **********.797504, "method": "POST", "uri": "/admin/ecommerce/products/edit/253", "ip": "127.0.0.1"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.035585, "end": **********.797519, "duration": 1.7619340419769287, "duration_str": "1.76s", "measures": [{"label": "Booting", "start": **********.035585, "relative_start": 0, "end": **********.807787, "relative_end": **********.807787, "duration": 0.****************, "duration_str": "772ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.807797, "relative_start": 0.***************, "end": **********.797522, "relative_end": 3.0994415283203125e-06, "duration": 0.****************, "duration_str": "990ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.825595, "relative_start": 0.****************, "end": **********.833025, "relative_end": **********.833025, "duration": 0.007430076599121094, "duration_str": "7.43ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.701152, "relative_start": 1.**************, "end": **********.794437, "relative_end": **********.794437, "duration": 0.*****************, "duration_str": "93.28ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": ********, "peak_usage_str": "52MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "11.x", "tooltip": {"Laravel Version": "11.44.2", "PHP Version": "8.3.17", "Environment": "localhost", "Debug Mode": "Enabled", "URL": "muhrak.gc", "Timezone": "UTC", "Locale": "en"}}, "views": {"count": 0, "nb_templates": 0, "templates": []}, "queries": {"count": 42, "nb_statements": 42, "nb_visible_statements": 42, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.12469999999999998, "accumulated_duration_str": "125ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 170}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}], "start": **********.84518, "duration": 0.00037, "duration_str": "370μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 0, "width_percent": 0.297}, {"sql": "select * from `ec_products` where `id` = '253' limit 1", "type": "query", "params": [], "bindings": ["253"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ImplicitRouteBinding.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ImplicitRouteBinding.php", "line": 61}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Router.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 961}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Middleware/SubstituteBindings.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php", "line": 42}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 209}], "start": **********.850241, "duration": 0.00038, "duration_str": "380μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 0.297, "width_percent": 0.305}, {"sql": "select `lang_locale`, `lang_code`, `lang_name`, `lang_flag`, `lang_is_rtl` from `languages` order by `lang_order` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 16, "namespace": null, "name": "platform/plugins/language/src/LanguageManager.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\language\\src\\LanguageManager.php", "line": 156}, {"index": 18, "namespace": null, "name": "platform/plugins/language/src/Providers/HookServiceProvider.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\language\\src\\Providers\\HookServiceProvider.php", "line": 105}, {"index": 22, "namespace": null, "name": "platform/core/base/helpers/action-filter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\helpers\\action-filter.php", "line": 39}, {"index": 24, "namespace": null, "name": "platform/core/base/src/Http/Middleware/AdminLocaleMiddleware.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Http\\Middleware\\AdminLocaleMiddleware.php", "line": 28}], "start": **********.854467, "duration": 0.00034, "duration_str": "340μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 0.601, "width_percent": 0.273}, {"sql": "select count(*) as aggregate from `ec_product_categories` where `id` = '40'", "type": "query", "params": [], "bindings": ["40"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/DatabasePresenceVerifier.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\DatabasePresenceVerifier.php", "line": 54}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Concerns/ValidatesAttributes.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Concerns\\ValidatesAttributes.php", "line": 984}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Concerns/ValidatesAttributes.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Concerns\\ValidatesAttributes.php", "line": 955}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Validator.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php", "line": 686}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Validator.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php", "line": 481}], "start": **********.030347, "duration": 0.00045, "duration_str": "450μs", "memory": 0, "memory_str": null, "filename": "DatabasePresenceVerifier.php:54", "source": {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/DatabasePresenceVerifier.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\DatabasePresenceVerifier.php", "line": 54}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FValidation%2FDatabasePresenceVerifier.php&line=54", "ajax": false, "filename": "DatabasePresenceVerifier.php", "line": "54"}, "connection": "muhrak", "explain": null, "start_percent": 0.874, "width_percent": 0.361}, {"sql": "select count(*) as aggregate from `ec_product_variations` where `ec_product_variations`.`configurable_product_id` = 253 and `ec_product_variations`.`configurable_product_id` is not null", "type": "query", "params": [], "bindings": [253], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "platform/plugins/ecommerce/src/Services/Products/StoreProductService.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Services\\Products\\StoreProductService.php", "line": 36}, {"index": 20, "namespace": null, "name": "platform/plugins/ecommerce/src/Http/Controllers/ProductController.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Http\\Controllers\\ProductController.php", "line": 162}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 44}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 266}], "start": **********.035636, "duration": 0.00035, "duration_str": "350μs", "memory": 0, "memory_str": null, "filename": "StoreProductService.php:36", "source": {"index": 19, "namespace": null, "name": "platform/plugins/ecommerce/src/Services/Products/StoreProductService.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Services\\Products\\StoreProductService.php", "line": 36}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FServices%2FProducts%2FStoreProductService.php&line=36", "ajax": false, "filename": "StoreProductService.php", "line": "36"}, "connection": "muhrak", "explain": null, "start_percent": 1.235, "width_percent": 0.281}, {"sql": "update `ec_products` set `content` = '<p>Application: This engine wire harness is specifically designed for the VOLVO380D model, ensuring compatibility and optimal performance.Material: Constructed from high-quality materials, providing durability and resistance to wear, heat, and environmental factors.Design: Engineered to match the exact specifications of the VOLVO380D engine, facilitating easy installation and reliable connections.Functionality: Enables efficient transmission of electrical signals between engine components, ensuring smooth operation and performance of the engine.Safety Features: Equipped with secure connectors and protective sheathing to safeguard against electrical shorts, abrasions, and external damage.</p>', `status` = 'published', `stock_status` = 'in_stock', `brand_id` = null, `barcode` = null, `main_type` = 'heavy-equipment', `ec_products`.`updated_at` = '2025-06-13 22:07:21' where `id` = 253", "type": "query", "params": [], "bindings": ["<p>Application: This engine wire harness is specifically designed for the VOLVO380D model, ensuring compatibility and optimal performance.Material: Constructed from high-quality materials, providing durability and resistance to wear, heat, and environmental factors.Design: Engineered to match the exact specifications of the VOLVO380D engine, facilitating easy installation and reliable connections.Functionality: Enables efficient transmission of electrical signals between engine components, ensuring smooth operation and performance of the engine.Safety Features: Equipped with secure connectors and protective sheathing to safeguard against electrical shorts, abrasions, and external damage.</p>", {"value": "published", "label": "Published"}, {"value": "in_stock", "label": "In stock"}, null, null, {"value": "heavy-equipment", "label": "Heavy Equipment"}, "2025-06-13 22:07:21", 253], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "platform/plugins/ecommerce/src/Services/Products/StoreProductService.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Services\\Products\\StoreProductService.php", "line": 91}, {"index": 15, "namespace": null, "name": "platform/plugins/ecommerce/src/Http/Controllers/ProductController.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Http\\Controllers\\ProductController.php", "line": 162}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 44}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 266}], "start": **********.068426, "duration": 0.016730000000000002, "duration_str": "16.73ms", "memory": 0, "memory_str": null, "filename": "StoreProductService.php:91", "source": {"index": 14, "namespace": null, "name": "platform/plugins/ecommerce/src/Services/Products/StoreProductService.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Services\\Products\\StoreProductService.php", "line": 91}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FServices%2FProducts%2FStoreProductService.php&line=91", "ajax": false, "filename": "StoreProductService.php", "line": "91"}, "connection": "muhrak", "explain": null, "start_percent": 1.516, "width_percent": 13.416}, {"sql": "select exists(select * from `ec_product_variations` where `ec_product_variations`.`configurable_product_id` = 253 and `ec_product_variations`.`configurable_product_id` is not null) as `exists`", "type": "query", "params": [], "bindings": [253], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 148}, {"index": 21, "namespace": null, "name": "platform/plugins/ecommerce/src/Services/Products/StoreProductService.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Services\\Products\\StoreProductService.php", "line": 91}, {"index": 22, "namespace": null, "name": "platform/plugins/ecommerce/src/Http/Controllers/ProductController.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Http\\Controllers\\ProductController.php", "line": 162}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 44}], "start": **********.092891, "duration": 0.0005899999999999999, "duration_str": "590μs", "memory": 0, "memory_str": null, "filename": "Product.php:148", "source": {"index": 14, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 148}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FModels%2FProduct.php&line=148", "ajax": false, "filename": "Product.php", "line": "148"}, "connection": "muhrak", "explain": null, "start_percent": 14.932, "width_percent": 0.473}, {"sql": "select * from `meta_boxes` where (`meta_key` = 'seo_meta' and `reference_id` = 253 and `reference_type` = 'Botble\\\\Ecommerce\\\\Models\\\\Product') limit 1", "type": "query", "params": [], "bindings": ["seo_meta", 253, "Botble\\Ecommerce\\Models\\Product"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 18, "namespace": null, "name": "platform/core/base/src/Supports/MetaBox.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Supports\\MetaBox.php", "line": 147}, {"index": 20, "namespace": null, "name": "platform/packages/seo-helper/src/SeoHelper.php", "file": "D:\\laragon\\www\\muhrak\\platform\\packages\\seo-helper\\src\\SeoHelper.php", "line": 171}, {"index": 22, "namespace": null, "name": "platform/packages/seo-helper/src/Listeners/UpdatedContentListener.php", "file": "D:\\laragon\\www\\muhrak\\platform\\packages\\seo-helper\\src\\Listeners\\UpdatedContentListener.php", "line": 15}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/helpers.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php", "line": 473}], "start": **********.210962, "duration": 0.0005, "duration_str": "500μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 15.405, "width_percent": 0.401}, {"sql": "insert into `meta_boxes` (`meta_key`, `reference_id`, `reference_type`, `meta_value`, `updated_at`, `created_at`) values ('seo_meta', 253, 'Bo<PERSON>ble\\\\Ecommerce\\\\Models\\\\Product', '[{\\\"index\\\":\\\"index\\\"}]', '2025-06-13 22:07:21', '2025-06-13 22:07:21')", "type": "query", "params": [], "bindings": ["seo_meta", 253, "Botble\\Ecommerce\\Models\\Product", "[{\"index\":\"index\"}]", "2025-06-13 22:07:21", "2025-06-13 22:07:21"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Supports/MetaBox.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Supports\\MetaBox.php", "line": 156}, {"index": 17, "namespace": null, "name": "platform/packages/seo-helper/src/SeoHelper.php", "file": "D:\\laragon\\www\\muhrak\\platform\\packages\\seo-helper\\src\\SeoHelper.php", "line": 171}, {"index": 19, "namespace": null, "name": "platform/packages/seo-helper/src/Listeners/UpdatedContentListener.php", "file": "D:\\laragon\\www\\muhrak\\platform\\packages\\seo-helper\\src\\Listeners\\UpdatedContentListener.php", "line": 15}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/helpers.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php", "line": 473}, {"index": 25, "namespace": null, "name": "platform/plugins/ecommerce/src/Http/Controllers/ProductController.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Http\\Controllers\\ProductController.php", "line": 162}], "start": **********.2159438, "duration": 0.00532, "duration_str": "5.32ms", "memory": 0, "memory_str": null, "filename": "MetaBox.php:156", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Supports/MetaBox.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Supports\\MetaBox.php", "line": 156}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FSupports%2FMetaBox.php&line=156", "ajax": false, "filename": "MetaBox.php", "line": "156"}, "connection": "muhrak", "explain": null, "start_percent": 15.806, "width_percent": 4.266}, {"sql": "select * from `slugs` where (`reference_type` = 'Botble\\\\Ecommerce\\\\Models\\\\Product' and `reference_id` = 253) limit 1", "type": "query", "params": [], "bindings": ["Botble\\Ecommerce\\Models\\Product", 253], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "platform/packages/slug/src/Listeners/UpdatedContentListener.php", "file": "D:\\laragon\\www\\muhrak\\platform\\packages\\slug\\src\\Listeners\\UpdatedContentListener.php", "line": 49}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/helpers.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php", "line": 473}, {"index": 23, "namespace": null, "name": "platform/plugins/ecommerce/src/Http/Controllers/ProductController.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Http\\Controllers\\ProductController.php", "line": 162}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.2413888, "duration": 0.0007700000000000001, "duration_str": "770μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 20.072, "width_percent": 0.617}, {"sql": "select `lang_id` from `languages` where `lang_is_default` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "platform/plugins/language/src/LanguageManager.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\language\\src\\LanguageManager.php", "line": 938}, {"index": 18, "namespace": null, "name": "platform/plugins/language/src/LanguageManager.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\language\\src\\LanguageManager.php", "line": 889}, {"index": 20, "namespace": null, "name": "platform/plugins/language/src/Listeners/UpdatedContentListener.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\language\\src\\Listeners\\UpdatedContentListener.php", "line": 16}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/helpers.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php", "line": 473}], "start": **********.300616, "duration": 0.0009699999999999999, "duration_str": "970μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 20.69, "width_percent": 0.778}, {"sql": "insert into `audit_histories` (`user_agent`, `ip_address`, `module`, `action`, `user_id`, `reference_user`, `reference_id`, `reference_name`, `type`, `created_at`, `updated_at`, `request`) values ('Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '127.0.0.1', 'product', 'updated', 1, 1, 253, 'VOLVO380D Engine Wire Harness', 'primary', '2025-06-13 22:07:21', '2025-06-13 22:07:21', '{\\\"name\\\":\\\"VOLVO380D Engine Wire Harness\\\",\\\"model\\\":\\\"[{\\\\\\\"value\\\\\\\":\\\\\\\"FTX08 G4\\\\\\\"},{\\\\\\\"value\\\\\\\":\\\\\\\"COHC15\\\\\\\"},{\\\\\\\"value\\\\\\\":\\\\\\\"VB 90 ELEC\\\\\\\"},{\\\\\\\"value\\\\\\\":\\\\\\\"NUMAC 3006\\\\\\\"},{\\\\\\\"value\\\\\\\":\\\\\\\"TE16S\\\\\\\"},{\\\\\\\"value\\\\\\\":\\\\\\\"VB 42 ELEC\\\\\\\"}]\\\",\\\"slug\\\":\\\"volvo380d-engine-wire-harness\\\",\\\"slug_id\\\":\\\"2901\\\",\\\"is_slug_editable\\\":\\\"1\\\",\\\"description\\\":null,\\\"content\\\":\\\"<p>Application: This engine wire harness is specifically designed for the VOLVO380D model, ensuring compatibility and optimal performance.Material: Constructed from high-quality materials, providing durability and resistance to wear, heat, and environmental factors.Design: Engineered to match the exact specifications of the VOLVO380D engine, facilitating easy installation and reliable connections.Functionality: Enables efficient transmission of electrical signals between engine components, ensuring smooth operation and performance of the engine.Safety Features: Equipped with secure connectors and protective sheathing to safeguard against electrical shorts, abrasions, and external damage.<\\\\/p>\\\",\\\"country_of_origin\\\":null,\\\"material\\\":null,\\\"applications\\\":null,\\\"images\\\":[null,\\\"709e6a50-90de-47d5-9f87-0cd52fc7d625.jpeg\\\",\\\"2472432b-1c18-4330-aaa1-9dc08a8db5ba.jpeg\\\"],\\\"product_type\\\":\\\"physical\\\",\\\"sale_type\\\":\\\"0\\\",\\\"sku\\\":\\\"MF-2443-QH9LW\\\",\\\"price\\\":\\\"0\\\",\\\"sale_price\\\":null,\\\"start_date\\\":null,\\\"end_date\\\":null,\\\"cost_per_item\\\":\\\"520\\\",\\\"product_id\\\":\\\"253\\\",\\\"barcode\\\":null,\\\"with_storehouse_management\\\":\\\"0\\\",\\\"quantity\\\":null,\\\"allow_checkout_when_out_of_stock\\\":\\\"0\\\",\\\"stock_status\\\":\\\"in_stock\\\",\\\"weight\\\":null,\\\"length\\\":null,\\\"wide\\\":null,\\\"height\\\":null,\\\"is_added_attributes\\\":\\\"0\\\",\\\"related_products\\\":null,\\\"faq_schema_config\\\":[[{\\\"key\\\":\\\"question\\\",\\\"value\\\":null},{\\\"key\\\":\\\"answer\\\",\\\"value\\\":null}]],\\\"seo_meta\\\":{\\\"seo_title\\\":null,\\\"seo_description\\\":null,\\\"index\\\":\\\"index\\\"},\\\"seo_meta_image\\\":null,\\\"language\\\":\\\"en_US\\\",\\\"status\\\":\\\"published\\\",\\\"store_id\\\":\\\"89\\\",\\\"is_featured\\\":\\\"0\\\",\\\"main_type\\\":\\\"heavy-equipment\\\",\\\"categories\\\":[\\\"40\\\"],\\\"brand_id\\\":null,\\\"image\\\":\\\"709e6a50-90de-47d5-9f87-0cd52fc7d625.jpeg\\\",\\\"minimum_order_quantity\\\":\\\"0\\\",\\\"maximum_order_quantity\\\":\\\"0\\\",\\\"tag\\\":\\\"[{\\\\\\\"value\\\\\\\":\\\\\\\"wire\\\\\\\"},{\\\\\\\"value\\\\\\\":\\\\\\\"wire harness\\\\\\\"}]\\\",\\\"submitter\\\":\\\"apply\\\"}')", "type": "query", "params": [], "bindings": ["Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "127.0.0.1", "product", "updated", 1, 1, 253, "VOLVO380D Engine Wire Harness", "primary", "2025-06-13 22:07:21", "2025-06-13 22:07:21", "{\"name\":\"VOLVO380D Engine Wire Harness\",\"model\":\"[{\\\"value\\\":\\\"FTX08 G4\\\"},{\\\"value\\\":\\\"COHC15\\\"},{\\\"value\\\":\\\"VB 90 ELEC\\\"},{\\\"value\\\":\\\"NUMAC 3006\\\"},{\\\"value\\\":\\\"TE16S\\\"},{\\\"value\\\":\\\"VB 42 ELEC\\\"}]\",\"slug\":\"volvo380d-engine-wire-harness\",\"slug_id\":\"2901\",\"is_slug_editable\":\"1\",\"description\":null,\"content\":\"<p>Application: This engine wire harness is specifically designed for the VOLVO380D model, ensuring compatibility and optimal performance.Material: Constructed from high-quality materials, providing durability and resistance to wear, heat, and environmental factors.Design: Engineered to match the exact specifications of the VOLVO380D engine, facilitating easy installation and reliable connections.Functionality: Enables efficient transmission of electrical signals between engine components, ensuring smooth operation and performance of the engine.Safety Features: Equipped with secure connectors and protective sheathing to safeguard against electrical shorts, abrasions, and external damage.<\\/p>\",\"country_of_origin\":null,\"material\":null,\"applications\":null,\"images\":[null,\"709e6a50-90de-47d5-9f87-0cd52fc7d625.jpeg\",\"2472432b-1c18-4330-aaa1-9dc08a8db5ba.jpeg\"],\"product_type\":\"physical\",\"sale_type\":\"0\",\"sku\":\"MF-2443-QH9LW\",\"price\":\"0\",\"sale_price\":null,\"start_date\":null,\"end_date\":null,\"cost_per_item\":\"520\",\"product_id\":\"253\",\"barcode\":null,\"with_storehouse_management\":\"0\",\"quantity\":null,\"allow_checkout_when_out_of_stock\":\"0\",\"stock_status\":\"in_stock\",\"weight\":null,\"length\":null,\"wide\":null,\"height\":null,\"is_added_attributes\":\"0\",\"related_products\":null,\"faq_schema_config\":[[{\"key\":\"question\",\"value\":null},{\"key\":\"answer\",\"value\":null}]],\"seo_meta\":{\"seo_title\":null,\"seo_description\":null,\"index\":\"index\"},\"seo_meta_image\":null,\"language\":\"en_US\",\"status\":\"published\",\"store_id\":\"89\",\"is_featured\":\"0\",\"main_type\":\"heavy-equipment\",\"categories\":[\"40\"],\"brand_id\":null,\"image\":\"709e6a50-90de-47d5-9f87-0cd52fc7d625.jpeg\",\"minimum_order_quantity\":\"0\",\"maximum_order_quantity\":\"0\",\"tag\":\"[{\\\"value\\\":\\\"wire\\\"},{\\\"value\\\":\\\"wire harness\\\"}]\",\"submitter\":\"apply\"}"], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "platform/plugins/audit-log/src/Listeners/AuditHandlerListener.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\audit-log\\src\\Listeners\\AuditHandlerListener.php", "line": 60}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/helpers.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php", "line": 473}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/helpers.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php", "line": 473}, {"index": 22, "namespace": null, "name": "platform/plugins/ecommerce/src/Http/Controllers/ProductController.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Http\\Controllers\\ProductController.php", "line": 162}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.413141, "duration": 0.0050999999999999995, "duration_str": "5.1ms", "memory": 0, "memory_str": null, "filename": "AuditHandlerListener.php:60", "source": {"index": 11, "namespace": null, "name": "platform/plugins/audit-log/src/Listeners/AuditHandlerListener.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\audit-log\\src\\Listeners\\AuditHandlerListener.php", "line": 60}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Faudit-log%2Fsrc%2FListeners%2FAuditHandlerListener.php&line=60", "ajax": false, "filename": "AuditHandlerListener.php", "line": "60"}, "connection": "muhrak", "explain": null, "start_percent": 21.468, "width_percent": 4.09}, {"sql": "select * from `ec_product_variations` where `ec_product_variations`.`configurable_product_id` = 253 and `ec_product_variations`.`configurable_product_id` is not null", "type": "query", "params": [], "bindings": [253], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "platform/plugins/ecommerce/src/Listeners/AddLanguageForVariantsListener.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Listeners\\AddLanguageForVariantsListener.php", "line": 20}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/helpers.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php", "line": 473}, {"index": 23, "namespace": null, "name": "platform/plugins/ecommerce/src/Http/Controllers/ProductController.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Http\\Controllers\\ProductController.php", "line": 162}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.446084, "duration": 0.0005, "duration_str": "500μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 25.557, "width_percent": 0.401}, {"sql": "delete from `meta_boxes` where (`meta_key` = 'faq_schema_config' and `reference_id` = 253 and `reference_type` = 'Botble\\\\Ecommerce\\\\Models\\\\Product')", "type": "query", "params": [], "bindings": ["faq_schema_config", 253, "Botble\\Ecommerce\\Models\\Product"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/core/base/src/Supports/MetaBox.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Supports\\MetaBox.php", "line": 201}, {"index": 14, "namespace": null, "name": "platform/plugins/faq/src/FaqSupport.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\faq\\src\\FaqSupport.php", "line": 69}, {"index": 15, "namespace": null, "name": "platform/plugins/ecommerce/src/Listeners/SaveProductFaqListener.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Listeners\\SaveProductFaqListener.php", "line": 23}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/helpers.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php", "line": 473}, {"index": 21, "namespace": null, "name": "platform/plugins/ecommerce/src/Http/Controllers/ProductController.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Http\\Controllers\\ProductController.php", "line": 162}], "start": **********.482167, "duration": 0.01706, "duration_str": "17.06ms", "memory": 0, "memory_str": null, "filename": "MetaBox.php:201", "source": {"index": 12, "namespace": null, "name": "platform/core/base/src/Supports/MetaBox.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Supports\\MetaBox.php", "line": 201}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FSupports%2FMetaBox.php&line=201", "ajax": false, "filename": "MetaBox.php", "line": "201"}, "connection": "muhrak", "explain": null, "start_percent": 25.958, "width_percent": 13.681}, {"sql": "select * from `meta_boxes` where (`meta_key` = 'faq_ids' and `reference_id` = 253 and `reference_type` = 'Botble\\\\Ecommerce\\\\Models\\\\Product') limit 1", "type": "query", "params": [], "bindings": ["faq_ids", 253, "Botble\\Ecommerce\\Models\\Product"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 18, "namespace": null, "name": "platform/core/base/src/Supports/MetaBox.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Supports\\MetaBox.php", "line": 147}, {"index": 20, "namespace": null, "name": "platform/plugins/ecommerce/src/Listeners/SaveProductFaqListener.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Listeners\\SaveProductFaqListener.php", "line": 26}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/helpers.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php", "line": 473}, {"index": 26, "namespace": null, "name": "platform/plugins/ecommerce/src/Http/Controllers/ProductController.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Http\\Controllers\\ProductController.php", "line": 162}], "start": **********.5040438, "duration": 0.03096, "duration_str": "30.96ms", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 39.639, "width_percent": 24.828}, {"sql": "select * from `ec_product_category_product` where `ec_product_category_product`.`product_id` = 253", "type": "query", "params": [], "bindings": [253], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/plugins/ecommerce/src/Services/Products/StoreProductService.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Services\\Products\\StoreProductService.php", "line": 99}, {"index": 16, "namespace": null, "name": "platform/plugins/ecommerce/src/Http/Controllers/ProductController.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Http\\Controllers\\ProductController.php", "line": 162}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 44}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 266}], "start": **********.547972, "duration": 0.00061, "duration_str": "610μs", "memory": 0, "memory_str": null, "filename": "StoreProductService.php:99", "source": {"index": 15, "namespace": null, "name": "platform/plugins/ecommerce/src/Services/Products/StoreProductService.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Services\\Products\\StoreProductService.php", "line": 99}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FServices%2FProducts%2FStoreProductService.php&line=99", "ajax": false, "filename": "StoreProductService.php", "line": "99"}, "connection": "muhrak", "explain": null, "start_percent": 64.467, "width_percent": 0.489}, {"sql": "select * from `ec_product_collection_products` where `ec_product_collection_products`.`product_id` = 253", "type": "query", "params": [], "bindings": [253], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/plugins/ecommerce/src/Services/Products/StoreProductService.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Services\\Products\\StoreProductService.php", "line": 101}, {"index": 16, "namespace": null, "name": "platform/plugins/ecommerce/src/Http/Controllers/ProductController.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Http\\Controllers\\ProductController.php", "line": 162}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 44}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 266}], "start": **********.5530128, "duration": 0.00109, "duration_str": "1.09ms", "memory": 0, "memory_str": null, "filename": "StoreProductService.php:101", "source": {"index": 15, "namespace": null, "name": "platform/plugins/ecommerce/src/Services/Products/StoreProductService.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Services\\Products\\StoreProductService.php", "line": 101}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FServices%2FProducts%2FStoreProductService.php&line=101", "ajax": false, "filename": "StoreProductService.php", "line": "101"}, "connection": "muhrak", "explain": null, "start_percent": 64.956, "width_percent": 0.874}, {"sql": "select * from `ec_product_label_products` where `ec_product_label_products`.`product_id` = 253", "type": "query", "params": [], "bindings": [253], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/plugins/ecommerce/src/Services/Products/StoreProductService.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Services\\Products\\StoreProductService.php", "line": 103}, {"index": 16, "namespace": null, "name": "platform/plugins/ecommerce/src/Http/Controllers/ProductController.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Http\\Controllers\\ProductController.php", "line": 162}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 44}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 266}], "start": **********.556669, "duration": 0.0058, "duration_str": "5.8ms", "memory": 0, "memory_str": null, "filename": "StoreProductService.php:103", "source": {"index": 15, "namespace": null, "name": "platform/plugins/ecommerce/src/Services/Products/StoreProductService.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Services\\Products\\StoreProductService.php", "line": 103}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FServices%2FProducts%2FStoreProductService.php&line=103", "ajax": false, "filename": "StoreProductService.php", "line": "103"}, "connection": "muhrak", "explain": null, "start_percent": 65.83, "width_percent": 4.651}, {"sql": "select * from `ec_tax_products` where `ec_tax_products`.`product_id` = 253", "type": "query", "params": [], "bindings": [253], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/plugins/ecommerce/src/Services/Products/StoreProductService.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Services\\Products\\StoreProductService.php", "line": 105}, {"index": 16, "namespace": null, "name": "platform/plugins/ecommerce/src/Http/Controllers/ProductController.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Http\\Controllers\\ProductController.php", "line": 162}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 44}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 266}], "start": **********.565501, "duration": 0.00061, "duration_str": "610μs", "memory": 0, "memory_str": null, "filename": "StoreProductService.php:105", "source": {"index": 15, "namespace": null, "name": "platform/plugins/ecommerce/src/Services/Products/StoreProductService.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Services\\Products\\StoreProductService.php", "line": 105}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FServices%2FProducts%2FStoreProductService.php&line=105", "ajax": false, "filename": "StoreProductService.php", "line": "105"}, "connection": "muhrak", "explain": null, "start_percent": 70.481, "width_percent": 0.489}, {"sql": "delete from `ec_product_related_relations` where `ec_product_related_relations`.`from_product_id` = 253", "type": "query", "params": [], "bindings": [253], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/plugins/ecommerce/src/Services/Products/StoreProductService.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Services\\Products\\StoreProductService.php", "line": 108}, {"index": 13, "namespace": null, "name": "platform/plugins/ecommerce/src/Http/Controllers/ProductController.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Http\\Controllers\\ProductController.php", "line": 162}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 44}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 266}], "start": **********.568644, "duration": 0.00051, "duration_str": "510μs", "memory": 0, "memory_str": null, "filename": "StoreProductService.php:108", "source": {"index": 12, "namespace": null, "name": "platform/plugins/ecommerce/src/Services/Products/StoreProductService.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Services\\Products\\StoreProductService.php", "line": 108}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FServices%2FProducts%2FStoreProductService.php&line=108", "ajax": false, "filename": "StoreProductService.php", "line": "108"}, "connection": "muhrak", "explain": null, "start_percent": 70.97, "width_percent": 0.409}, {"sql": "delete from `ec_product_cross_sale_relations` where `ec_product_cross_sale_relations`.`from_product_id` = 253", "type": "query", "params": [], "bindings": [253], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/plugins/ecommerce/src/Services/Products/StoreProductService.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Services\\Products\\StoreProductService.php", "line": 144}, {"index": 13, "namespace": null, "name": "platform/plugins/ecommerce/src/Http/Controllers/ProductController.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Http\\Controllers\\ProductController.php", "line": 162}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 44}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 266}], "start": **********.573731, "duration": 0.0005600000000000001, "duration_str": "560μs", "memory": 0, "memory_str": null, "filename": "StoreProductService.php:144", "source": {"index": 12, "namespace": null, "name": "platform/plugins/ecommerce/src/Services/Products/StoreProductService.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Services\\Products\\StoreProductService.php", "line": 144}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FServices%2FProducts%2FStoreProductService.php&line=144", "ajax": false, "filename": "StoreProductService.php", "line": "144"}, "connection": "muhrak", "explain": null, "start_percent": 71.379, "width_percent": 0.449}, {"sql": "select * from `ec_product_specification_attribute` where `ec_product_specification_attribute`.`product_id` = 253", "type": "query", "params": [], "bindings": [253], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/plugins/ecommerce/src/Services/Products/StoreProductService.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Services\\Products\\StoreProductService.php", "line": 162}, {"index": 16, "namespace": null, "name": "platform/plugins/ecommerce/src/Http/Controllers/ProductController.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Http\\Controllers\\ProductController.php", "line": 162}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 44}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 266}], "start": **********.5772429, "duration": 0.00182, "duration_str": "1.82ms", "memory": 0, "memory_str": null, "filename": "StoreProductService.php:162", "source": {"index": 15, "namespace": null, "name": "platform/plugins/ecommerce/src/Services/Products/StoreProductService.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Services\\Products\\StoreProductService.php", "line": 162}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FServices%2FProducts%2FStoreProductService.php&line=162", "ajax": false, "filename": "StoreProductService.php", "line": "162"}, "connection": "muhrak", "explain": null, "start_percent": 71.828, "width_percent": 1.46}, {"sql": "select `ec_product_tags`.*, `ec_product_tag_product`.`product_id` as `pivot_product_id`, `ec_product_tag_product`.`tag_id` as `pivot_tag_id` from `ec_product_tags` inner join `ec_product_tag_product` on `ec_product_tags`.`id` = `ec_product_tag_product`.`tag_id` where `ec_product_tag_product`.`product_id` = 253", "type": "query", "params": [], "bindings": [253], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 21, "namespace": null, "name": "platform/plugins/ecommerce/src/Services/StoreProductTagService.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Services\\StoreProductTagService.php", "line": 18}, {"index": 22, "namespace": null, "name": "platform/plugins/ecommerce/src/Http/Controllers/ProductController.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Http\\Controllers\\ProductController.php", "line": 163}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 44}], "start": **********.628007, "duration": 0.00073, "duration_str": "730μs", "memory": 0, "memory_str": null, "filename": "BaseModel.php:28", "source": {"index": 20, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseModel.php&line=28", "ajax": false, "filename": "BaseModel.php", "line": "28"}, "connection": "muhrak", "explain": null, "start_percent": 73.288, "width_percent": 0.585}, {"sql": "select `ec_product_models`.*, `ec_product_model_product`.`product_id` as `pivot_product_id`, `ec_product_model_product`.`model_id` as `pivot_model_id` from `ec_product_models` inner join `ec_product_model_product` on `ec_product_models`.`id` = `ec_product_model_product`.`model_id` where `ec_product_model_product`.`product_id` = 253", "type": "query", "params": [], "bindings": [253], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 21, "namespace": null, "name": "platform/plugins/ecommerce/src/Services/StoreProductModelService.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Services\\StoreProductModelService.php", "line": 18}, {"index": 22, "namespace": null, "name": "platform/plugins/ecommerce/src/Http/Controllers/ProductController.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Http\\Controllers\\ProductController.php", "line": 164}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 44}], "start": **********.632633, "duration": 0.00041, "duration_str": "410μs", "memory": 0, "memory_str": null, "filename": "BaseModel.php:28", "source": {"index": 20, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseModel.php&line=28", "ajax": false, "filename": "BaseModel.php", "line": "28"}, "connection": "muhrak", "explain": null, "start_percent": 73.873, "width_percent": 0.329}, {"sql": "delete from `ec_product_model_product` where `ec_product_model_product`.`product_id` = 253", "type": "query", "params": [], "bindings": [253], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/plugins/ecommerce/src/Services/StoreProductModelService.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Services\\StoreProductModelService.php", "line": 23}, {"index": 13, "namespace": null, "name": "platform/plugins/ecommerce/src/Http/Controllers/ProductController.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Http\\Controllers\\ProductController.php", "line": 164}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 44}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 266}], "start": **********.634441, "duration": 0.00043, "duration_str": "430μs", "memory": 0, "memory_str": null, "filename": "StoreProductModelService.php:23", "source": {"index": 12, "namespace": null, "name": "platform/plugins/ecommerce/src/Services/StoreProductModelService.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Services\\StoreProductModelService.php", "line": 23}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FServices%2FStoreProductModelService.php&line=23", "ajax": false, "filename": "StoreProductModelService.php", "line": "23"}, "connection": "muhrak", "explain": null, "start_percent": 74.202, "width_percent": 0.345}, {"sql": "select * from `ec_product_models` where `name` = 'FTX08 G4' limit 1", "type": "query", "params": [], "bindings": ["FTX08 G4"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "platform/plugins/ecommerce/src/Services/StoreProductModelService.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Services\\StoreProductModelService.php", "line": 32}, {"index": 18, "namespace": null, "name": "platform/plugins/ecommerce/src/Http/Controllers/ProductController.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Http\\Controllers\\ProductController.php", "line": 164}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 44}], "start": **********.636255, "duration": 0.00045, "duration_str": "450μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 74.547, "width_percent": 0.361}, {"sql": "select * from `ec_product_models` where `name` = 'COHC15' limit 1", "type": "query", "params": [], "bindings": ["COHC15"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "platform/plugins/ecommerce/src/Services/StoreProductModelService.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Services\\StoreProductModelService.php", "line": 32}, {"index": 18, "namespace": null, "name": "platform/plugins/ecommerce/src/Http/Controllers/ProductController.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Http\\Controllers\\ProductController.php", "line": 164}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 44}], "start": **********.6381412, "duration": 0.00202, "duration_str": "2.02ms", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 74.908, "width_percent": 1.62}, {"sql": "select * from `ec_product_models` where `name` = 'VB 90 ELEC' limit 1", "type": "query", "params": [], "bindings": ["VB 90 ELEC"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "platform/plugins/ecommerce/src/Services/StoreProductModelService.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Services\\StoreProductModelService.php", "line": 32}, {"index": 18, "namespace": null, "name": "platform/plugins/ecommerce/src/Http/Controllers/ProductController.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Http\\Controllers\\ProductController.php", "line": 164}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 44}], "start": **********.641687, "duration": 0.0004, "duration_str": "400μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 76.528, "width_percent": 0.321}, {"sql": "select * from `ec_product_models` where `name` = 'NUMAC 3006' limit 1", "type": "query", "params": [], "bindings": ["NUMAC 3006"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "platform/plugins/ecommerce/src/Services/StoreProductModelService.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Services\\StoreProductModelService.php", "line": 32}, {"index": 18, "namespace": null, "name": "platform/plugins/ecommerce/src/Http/Controllers/ProductController.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Http\\Controllers\\ProductController.php", "line": 164}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 44}], "start": **********.644564, "duration": 0.0005200000000000001, "duration_str": "520μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 76.848, "width_percent": 0.417}, {"sql": "select * from `ec_product_models` where `name` = 'TE16S' limit 1", "type": "query", "params": [], "bindings": ["TE16S"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "platform/plugins/ecommerce/src/Services/StoreProductModelService.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Services\\StoreProductModelService.php", "line": 32}, {"index": 18, "namespace": null, "name": "platform/plugins/ecommerce/src/Http/Controllers/ProductController.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Http\\Controllers\\ProductController.php", "line": 164}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 44}], "start": **********.647688, "duration": 0.0007199999999999999, "duration_str": "720μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 77.265, "width_percent": 0.577}, {"sql": "select * from `ec_product_models` where `name` = 'VB 42 ELEC' limit 1", "type": "query", "params": [], "bindings": ["VB 42 ELEC"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "platform/plugins/ecommerce/src/Services/StoreProductModelService.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Services\\StoreProductModelService.php", "line": 32}, {"index": 18, "namespace": null, "name": "platform/plugins/ecommerce/src/Http/Controllers/ProductController.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Http\\Controllers\\ProductController.php", "line": 164}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 44}], "start": **********.6508281, "duration": 0.00066, "duration_str": "660μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 77.843, "width_percent": 0.529}, {"sql": "select * from `ec_product_model_product` where `ec_product_model_product`.`product_id` = 253", "type": "query", "params": [], "bindings": [253], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/plugins/ecommerce/src/Services/StoreProductModelService.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Services\\StoreProductModelService.php", "line": 47}, {"index": 16, "namespace": null, "name": "platform/plugins/ecommerce/src/Http/Controllers/ProductController.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Http\\Controllers\\ProductController.php", "line": 164}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 44}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 266}], "start": **********.654325, "duration": 0.0006, "duration_str": "600μs", "memory": 0, "memory_str": null, "filename": "StoreProductModelService.php:47", "source": {"index": 15, "namespace": null, "name": "platform/plugins/ecommerce/src/Services/StoreProductModelService.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Services\\StoreProductModelService.php", "line": 47}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FServices%2FStoreProductModelService.php&line=47", "ajax": false, "filename": "StoreProductModelService.php", "line": "47"}, "connection": "muhrak", "explain": null, "start_percent": 78.372, "width_percent": 0.481}, {"sql": "insert into `ec_product_model_product` (`model_id`, `product_id`) values (40, 253)", "type": "query", "params": [], "bindings": [40, 253], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "platform/plugins/ecommerce/src/Services/StoreProductModelService.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Services\\StoreProductModelService.php", "line": 47}, {"index": 14, "namespace": null, "name": "platform/plugins/ecommerce/src/Http/Controllers/ProductController.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Http\\Controllers\\ProductController.php", "line": 164}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 44}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 266}], "start": **********.656128, "duration": 0.00471, "duration_str": "4.71ms", "memory": 0, "memory_str": null, "filename": "StoreProductModelService.php:47", "source": {"index": 13, "namespace": null, "name": "platform/plugins/ecommerce/src/Services/StoreProductModelService.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Services\\StoreProductModelService.php", "line": 47}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FServices%2FStoreProductModelService.php&line=47", "ajax": false, "filename": "StoreProductModelService.php", "line": "47"}, "connection": "muhrak", "explain": null, "start_percent": 78.853, "width_percent": 3.777}, {"sql": "insert into `ec_product_model_product` (`model_id`, `product_id`) values (37, 253)", "type": "query", "params": [], "bindings": [37, 253], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "platform/plugins/ecommerce/src/Services/StoreProductModelService.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Services\\StoreProductModelService.php", "line": 47}, {"index": 14, "namespace": null, "name": "platform/plugins/ecommerce/src/Http/Controllers/ProductController.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Http\\Controllers\\ProductController.php", "line": 164}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 44}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 266}], "start": **********.662719, "duration": 0.00385, "duration_str": "3.85ms", "memory": 0, "memory_str": null, "filename": "StoreProductModelService.php:47", "source": {"index": 13, "namespace": null, "name": "platform/plugins/ecommerce/src/Services/StoreProductModelService.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Services\\StoreProductModelService.php", "line": 47}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FServices%2FStoreProductModelService.php&line=47", "ajax": false, "filename": "StoreProductModelService.php", "line": "47"}, "connection": "muhrak", "explain": null, "start_percent": 82.63, "width_percent": 3.087}, {"sql": "insert into `ec_product_model_product` (`model_id`, `product_id`) values (34, 253)", "type": "query", "params": [], "bindings": [34, 253], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "platform/plugins/ecommerce/src/Services/StoreProductModelService.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Services\\StoreProductModelService.php", "line": 47}, {"index": 14, "namespace": null, "name": "platform/plugins/ecommerce/src/Http/Controllers/ProductController.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Http\\Controllers\\ProductController.php", "line": 164}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 44}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 266}], "start": **********.66829, "duration": 0.00443, "duration_str": "4.43ms", "memory": 0, "memory_str": null, "filename": "StoreProductModelService.php:47", "source": {"index": 13, "namespace": null, "name": "platform/plugins/ecommerce/src/Services/StoreProductModelService.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Services\\StoreProductModelService.php", "line": 47}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FServices%2FStoreProductModelService.php&line=47", "ajax": false, "filename": "StoreProductModelService.php", "line": "47"}, "connection": "muhrak", "explain": null, "start_percent": 85.718, "width_percent": 3.553}, {"sql": "insert into `ec_product_model_product` (`model_id`, `product_id`) values (11, 253)", "type": "query", "params": [], "bindings": [11, 253], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "platform/plugins/ecommerce/src/Services/StoreProductModelService.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Services\\StoreProductModelService.php", "line": 47}, {"index": 14, "namespace": null, "name": "platform/plugins/ecommerce/src/Http/Controllers/ProductController.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Http\\Controllers\\ProductController.php", "line": 164}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 44}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 266}], "start": **********.673991, "duration": 0.0046500000000000005, "duration_str": "4.65ms", "memory": 0, "memory_str": null, "filename": "StoreProductModelService.php:47", "source": {"index": 13, "namespace": null, "name": "platform/plugins/ecommerce/src/Services/StoreProductModelService.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Services\\StoreProductModelService.php", "line": 47}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FServices%2FStoreProductModelService.php&line=47", "ajax": false, "filename": "StoreProductModelService.php", "line": "47"}, "connection": "muhrak", "explain": null, "start_percent": 89.27, "width_percent": 3.729}, {"sql": "insert into `ec_product_model_product` (`model_id`, `product_id`) values (19, 253)", "type": "query", "params": [], "bindings": [19, 253], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "platform/plugins/ecommerce/src/Services/StoreProductModelService.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Services\\StoreProductModelService.php", "line": 47}, {"index": 14, "namespace": null, "name": "platform/plugins/ecommerce/src/Http/Controllers/ProductController.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Http\\Controllers\\ProductController.php", "line": 164}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 44}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 266}], "start": **********.679874, "duration": 0.00352, "duration_str": "3.52ms", "memory": 0, "memory_str": null, "filename": "StoreProductModelService.php:47", "source": {"index": 13, "namespace": null, "name": "platform/plugins/ecommerce/src/Services/StoreProductModelService.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Services\\StoreProductModelService.php", "line": 47}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FServices%2FStoreProductModelService.php&line=47", "ajax": false, "filename": "StoreProductModelService.php", "line": "47"}, "connection": "muhrak", "explain": null, "start_percent": 92.999, "width_percent": 2.823}, {"sql": "insert into `ec_product_model_product` (`model_id`, `product_id`) values (31, 253)", "type": "query", "params": [], "bindings": [31, 253], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "platform/plugins/ecommerce/src/Services/StoreProductModelService.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Services\\StoreProductModelService.php", "line": 47}, {"index": 14, "namespace": null, "name": "platform/plugins/ecommerce/src/Http/Controllers/ProductController.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Http\\Controllers\\ProductController.php", "line": 164}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 44}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 266}], "start": **********.685151, "duration": 0.00362, "duration_str": "3.62ms", "memory": 0, "memory_str": null, "filename": "StoreProductModelService.php:47", "source": {"index": 13, "namespace": null, "name": "platform/plugins/ecommerce/src/Services/StoreProductModelService.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Services\\StoreProductModelService.php", "line": 47}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FServices%2FStoreProductModelService.php&line=47", "ajax": false, "filename": "StoreProductModelService.php", "line": "47"}, "connection": "muhrak", "explain": null, "start_percent": 95.822, "width_percent": 2.903}, {"sql": "select count(*) as aggregate from `ec_product_variations` where `ec_product_variations`.`configurable_product_id` = 253 and `ec_product_variations`.`configurable_product_id` is not null", "type": "query", "params": [], "bindings": [253], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "platform/plugins/ecommerce/src/Http/Controllers/ProductController.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Http\\Controllers\\ProductController.php", "line": 205}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 44}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 266}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 212}], "start": **********.690803, "duration": 0.0004, "duration_str": "400μs", "memory": 0, "memory_str": null, "filename": "ProductController.php:205", "source": {"index": 19, "namespace": null, "name": "platform/plugins/ecommerce/src/Http/Controllers/ProductController.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Http\\Controllers\\ProductController.php", "line": 205}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FHttp%2FControllers%2FProductController.php&line=205", "ajax": false, "filename": "ProductController.php", "line": "205"}, "connection": "muhrak", "explain": null, "start_percent": 98.725, "width_percent": 0.321}, {"sql": "delete from `ec_product_with_attribute_set` where `ec_product_with_attribute_set`.`product_id` = 253", "type": "query", "params": [], "bindings": [253], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/plugins/ecommerce/src/Http/Controllers/ProductController.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Http\\Controllers\\ProductController.php", "line": 206}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 44}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 266}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 212}], "start": **********.692489, "duration": 0.00034, "duration_str": "340μs", "memory": 0, "memory_str": null, "filename": "ProductController.php:206", "source": {"index": 12, "namespace": null, "name": "platform/plugins/ecommerce/src/Http/Controllers/ProductController.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Http\\Controllers\\ProductController.php", "line": 206}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FHttp%2FControllers%2FProductController.php&line=206", "ajax": false, "filename": "ProductController.php", "line": "206"}, "connection": "muhrak", "explain": null, "start_percent": 99.046, "width_percent": 0.273}, {"sql": "select `product_id` from `ec_product_variations` where `ec_product_variations`.`configurable_product_id` = 253 and `ec_product_variations`.`configurable_product_id` is not null", "type": "query", "params": [], "bindings": [253], "hints": null, "show_copy": true, "backtrace": [{"index": 17, "namespace": null, "name": "platform/plugins/ecommerce/src/Http/Controllers/ProductController.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Http\\Controllers\\ProductController.php", "line": 221}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 44}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 266}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 212}], "start": **********.6943982, "duration": 0.00044, "duration_str": "440μs", "memory": 0, "memory_str": null, "filename": "ProductController.php:221", "source": {"index": 17, "namespace": null, "name": "platform/plugins/ecommerce/src/Http/Controllers/ProductController.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Http\\Controllers\\ProductController.php", "line": 221}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FHttp%2FControllers%2FProductController.php&line=221", "ajax": false, "filename": "ProductController.php", "line": "221"}, "connection": "muhrak", "explain": null, "start_percent": 99.318, "width_percent": 0.353}, {"sql": "update `ec_products` set `status` = 'published', `ec_products`.`updated_at` = '2025-06-13 22:07:21' where 0 = 1", "type": "query", "params": [], "bindings": [{"value": "published", "label": "Published"}, "2025-06-13 22:07:21"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/plugins/ecommerce/src/Http/Controllers/ProductController.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Http\\Controllers\\ProductController.php", "line": 223}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 44}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 266}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 212}], "start": **********.698166, "duration": 0.00041, "duration_str": "410μs", "memory": 0, "memory_str": null, "filename": "ProductController.php:223", "source": {"index": 12, "namespace": null, "name": "platform/plugins/ecommerce/src/Http/Controllers/ProductController.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Http\\Controllers\\ProductController.php", "line": 223}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FHttp%2FControllers%2FProductController.php&line=223", "ajax": false, "filename": "ProductController.php", "line": "223"}, "connection": "muhrak", "explain": null, "start_percent": 99.671, "width_percent": 0.329}]}, "models": {"data": {"Botble\\Ecommerce\\Models\\ProductModel": {"value": 6, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FModels%2FProductModel.php&line=1", "ajax": false, "filename": "ProductModel.php", "line": "?"}}, "Botble\\Language\\Models\\Language": {"value": 3, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Flanguage%2Fsrc%2FModels%2FLanguage.php&line=1", "ajax": false, "filename": "Language.php", "line": "?"}}, "Botble\\Ecommerce\\Models\\ProductTag": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FModels%2FProductTag.php&line=1", "ajax": false, "filename": "ProductTag.php", "line": "?"}}, "Botble\\ACL\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Facl%2Fsrc%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Botble\\Ecommerce\\Models\\Product": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FModels%2FProduct.php&line=1", "ajax": false, "filename": "Product.php", "line": "?"}}, "Botble\\Slug\\Models\\Slug": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fpackages%2Fslug%2Fsrc%2FModels%2FSlug.php&line=1", "ajax": false, "filename": "Slug.php", "line": "?"}}, "Botble\\Base\\Models\\MetaBox": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FMetaBox.php&line=1", "ajax": false, "filename": "MetaBox.php", "line": "?"}}}, "count": 15, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "request": {"data": {"status": "302 Found", "full_url": "https://muhrak.gc/admin/ecommerce/products/edit/253", "action_name": "products.edit.update", "controller_action": "Botble\\Ecommerce\\Http\\Controllers\\ProductController@update", "uri": "POST admin/ecommerce/products/edit/{product}", "controller": "Botble\\Ecommerce\\Http\\Controllers\\ProductController@update<a href=\"phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FHttp%2FControllers%2FProductController.php&line=153\" class=\"phpdebugbar-widgets-editor-link\"></a>", "namespace": "Botble\\Ecommerce\\Http\\Controllers", "prefix": "admin/ecommerce/products", "file": "<a href=\"phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FHttp%2FControllers%2FProductController.php&line=153\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">platform/plugins/ecommerce/src/Http/Controllers/ProductController.php:153-229</a>", "middleware": "web, core, auth", "duration": "1.76s", "peak_memory": "54MB", "response": "Redirect to https://muhrak.gc/admin/ecommerce/products/edit/253", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-170870726 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-170870726\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1082430477 data-indent-pad=\"  \"><span class=sf-dump-note>array:47</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">56w3qOLQ2WezgBAffsP49hPOj3d80BP8siOqKnfZ</span>\"\n  \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"29 characters\">VOLVO380D Engine Wire Harness</span>\"\n  \"<span class=sf-dump-key>model</span>\" => \"<span class=sf-dump-str title=\"128 characters\">[{&quot;value&quot;:&quot;FTX08 G4&quot;},{&quot;value&quot;:&quot;COHC15&quot;},{&quot;value&quot;:&quot;VB 90 ELEC&quot;},{&quot;value&quot;:&quot;NUMAC 3006&quot;},{&quot;value&quot;:&quot;TE16S&quot;},{&quot;value&quot;:&quot;VB 42 ELEC&quot;}]</span>\"\n  \"<span class=sf-dump-key>slug</span>\" => \"<span class=sf-dump-str title=\"29 characters\">volvo380d-engine-wire-harness</span>\"\n  \"<span class=sf-dump-key>slug_id</span>\" => \"<span class=sf-dump-str title=\"4 characters\">2901</span>\"\n  \"<span class=sf-dump-key>is_slug_editable</span>\" => \"<span class=sf-dump-str>1</span>\"\n  \"<span class=sf-dump-key>description</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>content</span>\" => \"<span class=sf-dump-str title=\"700 characters\">&lt;p&gt;Application: This engine wire harness is specifically designed for the VOLVO380D model, ensuring compatibility and optimal performance.Material: Constructed from high-quality materials, providing durability and resistance to wear, heat, and environmental factors.Design: Engineered to match the exact specifications of the VOLVO380D engine, facilitating easy installation and reliable connections.Functionality: Enables efficient transmission of electrical signals between engine components, ensuring smooth operation and performance of the engine.Safety Features: Equipped with secure connectors and protective sheathing to safeguard against electrical shorts, abrasions, and external damage.&lt;/p&gt;</span>\"\n  \"<span class=sf-dump-key>country_of_origin</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>material</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>applications</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>images</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => <span class=sf-dump-const>null</span>\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"41 characters\">709e6a50-90de-47d5-9f87-0cd52fc7d625.jpeg</span>\"\n    <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"41 characters\">2472432b-1c18-4330-aaa1-9dc08a8db5ba.jpeg</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>product_type</span>\" => \"<span class=sf-dump-str title=\"8 characters\">physical</span>\"\n  \"<span class=sf-dump-key>sale_type</span>\" => \"<span class=sf-dump-str>0</span>\"\n  \"<span class=sf-dump-key>sku</span>\" => \"<span class=sf-dump-str title=\"13 characters\">MF-2443-QH9LW</span>\"\n  \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str>0</span>\"\n  \"<span class=sf-dump-key>sale_price</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>start_date</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>end_date</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cost_per_item</span>\" => \"<span class=sf-dump-str title=\"3 characters\">520</span>\"\n  \"<span class=sf-dump-key>product_id</span>\" => \"<span class=sf-dump-str title=\"3 characters\">253</span>\"\n  \"<span class=sf-dump-key>barcode</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>with_storehouse_management</span>\" => \"<span class=sf-dump-str>0</span>\"\n  \"<span class=sf-dump-key>quantity</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>allow_checkout_when_out_of_stock</span>\" => \"<span class=sf-dump-str>0</span>\"\n  \"<span class=sf-dump-key>stock_status</span>\" => \"<span class=sf-dump-str title=\"8 characters\">in_stock</span>\"\n  \"<span class=sf-dump-key>weight</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>length</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>wide</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>height</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>is_added_attributes</span>\" => \"<span class=sf-dump-str>0</span>\"\n  \"<span class=sf-dump-key>related_products</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>faq_schema_config</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>key</span>\" => \"<span class=sf-dump-str title=\"8 characters\">question</span>\"\n        \"<span class=sf-dump-key>value</span>\" => <span class=sf-dump-const>null</span>\n      </samp>]\n      <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>key</span>\" => \"<span class=sf-dump-str title=\"6 characters\">answer</span>\"\n        \"<span class=sf-dump-key>value</span>\" => <span class=sf-dump-const>null</span>\n      </samp>]\n    </samp>]\n  </samp>]\n  \"<span class=sf-dump-key>seo_meta</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>seo_title</span>\" => <span class=sf-dump-const>null</span>\n    \"<span class=sf-dump-key>seo_description</span>\" => <span class=sf-dump-const>null</span>\n    \"<span class=sf-dump-key>index</span>\" => \"<span class=sf-dump-str title=\"5 characters\">index</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>seo_meta_image</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>language</span>\" => \"<span class=sf-dump-str title=\"5 characters\">en_US</span>\"\n  \"<span class=sf-dump-key>status</span>\" => \"<span class=sf-dump-str title=\"9 characters\">published</span>\"\n  \"<span class=sf-dump-key>store_id</span>\" => \"<span class=sf-dump-str title=\"2 characters\">89</span>\"\n  \"<span class=sf-dump-key>is_featured</span>\" => \"<span class=sf-dump-str>0</span>\"\n  \"<span class=sf-dump-key>main_type</span>\" => \"<span class=sf-dump-str title=\"15 characters\">heavy-equipment</span>\"\n  \"<span class=sf-dump-key>categories</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">40</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>brand_id</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>image</span>\" => \"<span class=sf-dump-str title=\"41 characters\">709e6a50-90de-47d5-9f87-0cd52fc7d625.jpeg</span>\"\n  \"<span class=sf-dump-key>minimum_order_quantity</span>\" => \"<span class=sf-dump-str>0</span>\"\n  \"<span class=sf-dump-key>maximum_order_quantity</span>\" => \"<span class=sf-dump-str>0</span>\"\n  \"<span class=sf-dump-key>tag</span>\" => \"<span class=sf-dump-str title=\"43 characters\">[{&quot;value&quot;:&quot;wire&quot;},{&quot;value&quot;:&quot;wire harness&quot;}]</span>\"\n  \"<span class=sf-dump-key>submitter</span>\" => \"<span class=sf-dump-str title=\"5 characters\">apply</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1082430477\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-932377987 data-indent-pad=\"  \"><span class=sf-dump-note>array:22</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">muhrak.gc</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">6943</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pragma</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">https://muhrak.gc</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>dnt</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"68 characters\">multipart/form-data; boundary=----WebKitFormBoundaryclf4u1errhfR7MKE</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"51 characters\">https://muhrak.gc/admin/ecommerce/products/edit/253</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"50 characters\">en-US,en;q=0.9,ur;q=0.8,pl;q=0.7,ru;q=0.6,ar;q=0.5</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3140 characters\">cookie_for_consent=1; botble_footprints_cookie=eyJpdiI6InJtenpwcG01ZldyRCs2TFV4YUYrYUE9PSIsInZhbHVlIjoiamVKQ2MwT2VxdDI3L0pXTXdlMURsY0JJSHdaWVp5T1VoSUNSbEpIZ0pZcHdKWGZId3lBLzJ2b1pIUXVYUUxKRjdMY3k5Q1VpTWRFd3Ftbzd6L3UyeHUvdWFpdTAwUXBNZER4MDc5MGJjOXQxQXhIazNiaTI0TU42YXJkbHNVek8iLCJtYWMiOiI3YWM1Njc0ODBhMzFhMjBlOWM2NzQ2YTE1ODRkNGM0ODk5YTNhYjE5ZjliNjY3NGI2MzA5MjMxNGJhNDliZjJjIiwidGFnIjoiIn0%3D; botble_footprints_cookie_data=eyJpdiI6ImozUjJ6OXN5RkhxbXdCYmc5K1JURlE9PSIsInZhbHVlIjoib3plTVp0NnFEZVhoU1NQejZBMklhY2RqSEtwTmNvaG1SeWtpbFkvZmVZb21QUmg4VTFvRVlPbUUzd1NGQ3J6dFZVT0hmdDIrTWd0SGF1Y28yYWx1VWJYdTJ0OUFvdkxJM3U3UktZNXpBL1pReU8xcDI0S3AxSmRkVzVHaHhyZHlia1ptb0wrTUhkNHRPbXhoY2pNL2JoZ2JXVGNTaHI2VjRIRW1HOUdiVHAwN2xxdkJLazdwZDFadCtJaTNlTk9HT2JmWms2Vnp5bGorWkxFd1dvaStjUHlEY1ZQR2tYOW5tREUxV25Vek1jbDFkenJlZENXR3VIaHM4aFdLaDRVNjViY21idXByU3ZQQVFCU2gxcVNnYW5zbERUamgxZjNUMVRIWEt4V0tUbGxFRDc5VzhDNkZRZ0g1QXRjU3Rna3ZTQlRwZjVScFM1eEdzUmNhSlAxaHpIZ2kxdVEvOG51K1FlVmhuSTBGdUZxYXdkdGdWLzFDdW1nZ0MvdDFnTit6STdZd3JJWnVCNGZZSTNFRWtjaTJndzZVT3prNEkrb1c0cTFFaGFWckxiUUtVSFVsYXlWSWxnUSttR2dxYlo0K0dCY1NzRDBGdzVNZGdLTnFOZG43VllRTkFORnIrZlk1SEt3ZFkvNjYwdjJFWmFJaCtPUVh4TjFZejFleUQvU09oeERvVkdyVVJ2d0ZkM25TZXU1OGNZOE5PWTJWVmV4WWpsWUE2Zmc0dHV3PSIsIm1hYyI6IjYwMTc0ODhiZDNjN2VkZGVmZjliNmYyNWU0ZTU2ZWE5YjhkMjllNzk1ZDAxNGU2ZWU3MDhkOTA3NzM3OTFjNjUiLCJ0YWciOiIifQ%3D%3D; remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6IktsRlR4UnBrM0szS1Y0SUxrVVlUdHc9PSIsInZhbHVlIjoibVgzWjBpSHR4T2o4NlJBc3ZWNEVGZGVXS0xzU3NlNjRPOXFyNVFIOVVkbCt1ODRxbVZFNkNaODlSa3BORzF3bTJEb3Rkb0RVTVVjZHNBVFZiVnQ2TlhLOVBNVkJhVUIzb0ZZV2VTOER2dEJjd1ZZZEw3OWg4UEV4OXZqSjQwR2hsbERhOTdCQ1puTTJGRG1nMnozejdpcXp0NllsWUVzYWl5SjFlZGhYVWdUNmYyY2xRVXdXeG9UU3M3S1kxMnVxbVc5bktlTHFEOHNJbDlUV2JPamxrK3AwangreXlUMmtBTVhRdlN0SHQwcz0iLCJtYWMiOiIzNTliMzJjOTQxODc2NDdkNDI0YTBiNjZkMzUzZjEzNzZjYWMwYzlmNDQ2NGZhZjJiOTk5M2NhYTc0YmEyOGY5IiwidGFnIjoiIn0%3D; remember_customer_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6Ijl6MUxxZXpoME96cm9sVGNGeUlyaEE9PSIsInZhbHVlIjoiWlR1dnBBRnpBZWF2c1I3dndid2M5UllhSUVPN0Fqa1p0K0RGelpiVkRpditYckx0YWZ2RVZFbTMwUGVnVGh4ODc5VFBseUtFaytWNWpRalB1QVhJbHZONFhqaFc2UjdjTlI1SlR0TDAwMS9ldmZxN1F5aTZpaWdnaFNOaGs0VmRWMlBoNkhMeTZKSU1hVTA3UHRKNUJKb3hPUEJVUWtEcjc2RFZmQ3NVanJEeHNHMU1jc3QzQ2N5blJKUUJVaFNsUEFQSmVKSHBiTjhtVmU2OEUwY1dBMTNyblVPOENzcTlRcHBOOTVuTWc1OD0iLCJtYWMiOiJhNzJhNzEyNGRjOTM0ZmVhMTIyNDU0ZmM0Mzc2MDQzZjI3MjNjNmM4NTUwMGQ2ZTZlNmZjM2NlMWZkYTgyMGM0IiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6ImVjVkliUzNlYnQ1SVRRa3B4eVVqNEE9PSIsInZhbHVlIjoiMXNmdXRlM2h2RW9YaUhrYW1QN3cwZ0RnZFZqT1NUcS9VOFFHWm5jMUVPYWxnakozK0tDcFFLSm9YSkx1dmNrekI1ZGlpUkp0bFVZemJiNW9xV2ZFUnBTZWxFRmVIV2lLa3NhN1Y1d2pMVklBVmtKQTFTMUZiSU9yUGVQQmRsZHciLCJtYWMiOiIyNjM3OWM4YzkyMGMwYmI3MWZmNGI1MGI0ZjYyZDNkY2Y5YjU5ZDNjODg4MTM0YzE3ZWZhYmZmZTBlYWNkZWFjIiwidGFnIjoiIn0%3D; botble_session=eyJpdiI6IlVVaEdORGJZb1NLbUZZclBpb3BIc0E9PSIsInZhbHVlIjoiQ20wNHdlLzg2Y003T01CaUVvT01DMWdKMk8wQmprejU3Tzk2NTRVVXdwVUxkU3lmYTlrODBEZjFoby91dkVKcStyWWI3elBsbmp0WFB0VDJ6TitETGI2R0tzQzhyZEZ6OGFuazVhLzJXK2RJY2llc2RIaFdISEY0bFZKQmQ0RjgiLCJtYWMiOiJjZjk5NTc5OTE4ZWE4MjhmYjU3ZDcxOTM1YzUwYWIzZWEwZTFhY2JmNmI3MWI1MTg2NDdhNjZjMzA0MDhhYjAwIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-932377987\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cookie_for_consent</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>botble_footprints_cookie</span>\" => \"<span class=sf-dump-str title=\"40 characters\">14908b951300852119d2c46221b4b11a7e2b8107</span>\"\n  \"<span class=sf-dump-key>botble_footprints_cookie_data</span>\" => \"<span class=sf-dump-str title=\"359 characters\">{&quot;footprint&quot;:&quot;14908b951300852119d2c46221b4b11a7e2b8107&quot;,&quot;ip&quot;:&quot;127.0.0.1&quot;,&quot;landing_domain&quot;:&quot;muhrak.gc&quot;,&quot;landing_page&quot;:&quot;install\\/welcome&quot;,&quot;landing_params&quot;:null,&quot;referral&quot;:null,&quot;gclid&quot;:null,&quot;fclid&quot;:null,&quot;utm_source&quot;:null,&quot;utm_campaign&quot;:null,&quot;utm_medium&quot;:null,&quot;utm_term&quot;:null,&quot;utm_content&quot;:null,&quot;referrer_url&quot;:&quot;http:\\/\\/localhost\\/&quot;,&quot;referrer_domain&quot;:&quot;localhost&quot;}</span>\"\n  \"<span class=sf-dump-key>remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => \"<span class=sf-dump-str title=\"123 characters\">1|lEoydfVrZKIFerelBjYotnW4P2e0TuoqJSFCOOr89XMprC6ygbibdEjl3rvX|$2y$12$6oEMzkNhsgbeo4WDg72G9e2nWxuvMvozEUdkIQ0AfpRz3PJNQ7XzG</span>\"\n  \"<span class=sf-dump-key>remember_customer_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => \"<span class=sf-dump-str title=\"123 characters\">2|eZ0awTe8bfQLceQTos5gvZ7R55kW3IiLBR997mfYDDJcPndE2TcWxwpqf39P|$2y$12$EU5h35Jju9igRCRES6rqCuE42l30uwJ94rSesc56rboRI0xQqYy/G</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">56w3qOLQ2WezgBAffsP49hPOj3d80BP8siOqKnfZ</span>\"\n  \"<span class=sf-dump-key>botble_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">NlmohzskXFfa1956yAEyoTgVOMjJqryOKOFtoCn1</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-961418454 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 13 Jun 2025 22:07:21 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"51 characters\">https://muhrak.gc/admin/ecommerce/products/edit/253</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-961418454\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-734223928 data-indent-pad=\"  \"><span class=sf-dump-note>array:11</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">56w3qOLQ2WezgBAffsP49hPOj3d80BP8siOqKnfZ</span>\"\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>language</span>\" => \"<span class=sf-dump-str title=\"2 characters\">en</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"51 characters\">https://muhrak.gc/admin/ecommerce/products/edit/253</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">success_msg</span>\"\n    </samp>]\n  </samp>]\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>login_customer_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>2</span>\n  \"<span class=sf-dump-key>locale_direction</span>\" => <span class=sf-dump-const>false</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n  \"<span class=sf-dump-key>viewed_product</span>\" => <span class=sf-dump-note>array:7</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-key>10811</span> => <span class=sf-dump-num>1749849428</span>\n    <span class=sf-dump-key>129</span> => <span class=sf-dump-num>1749849548</span>\n    <span class=sf-dump-key>119</span> => <span class=sf-dump-num>1749849650</span>\n    <span class=sf-dump-key>277</span> => <span class=sf-dump-num>1749849868</span>\n    <span class=sf-dump-key>115</span> => <span class=sf-dump-num>1749849904</span>\n    <span class=sf-dump-key>253</span> => <span class=sf-dump-num>1749850000</span>\n    <span class=sf-dump-key>989</span> => <span class=sf-dump-num>1749850593</span>\n  </samp>]\n  \"<span class=sf-dump-key>success_msg</span>\" => \"<span class=sf-dump-str title=\"20 characters\">Updated successfully</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-734223928\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "302 Found", "full_url": "https://muhrak.gc/admin/ecommerce/products/edit/253", "action_name": "products.edit.update", "controller_action": "Botble\\Ecommerce\\Http\\Controllers\\ProductController@update"}, "badge": "302 Found"}}